{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;\n\n// Client for browser/frontend use\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Admin client for server-side operations\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {\n  auth: {\n    autoRefreshToken: false,\n    persistSession: false\n  }\n});\n\n// Database Tables\nexport const TABLES = {\n  USERS: 'users',\n  HOROSCOPES: 'horoscopes',\n  DAILY_GUIDES: 'daily_guides',\n  QR_CODE_MAPPINGS: 'qr_code_mappings',\n  TRANSLATION_CACHE: 'translation_cache'\n} as const;\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAGzD,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,oBAAoB;IACzE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;AAGO,MAAM,SAAS;IACpB,OAAO;IACP,YAAY;IACZ,cAAc;IACd,kBAAkB;IAClB,mBAAmB;AACrB", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/translation.ts"], "sourcesContent": ["import { supabase, TABLES } from '@/lib/supabase';\n\nexport async function translateText(\n  text: string, \n  targetLanguage: 'en' | 'si',\n  sourceLanguage: 'en' | 'si' = 'en'\n): Promise<string> {\n  // If source and target are the same, return original text\n  if (sourceLanguage === targetLanguage) {\n    return text;\n  }\n\n  // Check cache first\n  const cachedTranslation = await getCachedTranslation(text, sourceLanguage, targetLanguage);\n  if (cachedTranslation) {\n    return cachedTranslation;\n  }\n\n  try {\n    // Call Gemini API for translation\n    const translatedText = await callGeminiTranslation(text, sourceLanguage, targetLanguage);\n    \n    // Cache the translation\n    await cacheTranslation(text, translatedText, sourceLanguage, targetLanguage);\n    \n    return translatedText;\n  } catch (error) {\n    console.error('Translation error:', error);\n    // Return original text if translation fails\n    return text;\n  }\n}\n\nasync function getCachedTranslation(\n  originalText: string,\n  sourceLanguage: 'en' | 'si',\n  targetLanguage: 'en' | 'si'\n): Promise<string | null> {\n  try {\n    const { data, error } = await supabase\n      .from(TABLES.TRANSLATION_CACHE)\n      .select('translated_text')\n      .eq('original_text', originalText)\n      .eq('source_language', sourceLanguage)\n      .eq('target_language', targetLanguage)\n      .single();\n\n    if (error || !data) {\n      return null;\n    }\n\n    return data.translated_text;\n  } catch (error) {\n    console.error('Error fetching cached translation:', error);\n    return null;\n  }\n}\n\nasync function cacheTranslation(\n  originalText: string,\n  translatedText: string,\n  sourceLanguage: 'en' | 'si',\n  targetLanguage: 'en' | 'si'\n): Promise<void> {\n  try {\n    await supabase\n      .from(TABLES.TRANSLATION_CACHE)\n      .insert({\n        original_text: originalText,\n        translated_text: translatedText,\n        source_language: sourceLanguage,\n        target_language: targetLanguage\n      });\n  } catch (error) {\n    console.error('Error caching translation:', error);\n  }\n}\n\nasync function callGeminiTranslation(\n  text: string,\n  sourceLanguage: 'en' | 'si',\n  targetLanguage: 'en' | 'si'\n): Promise<string> {\n  const response = await fetch('/api/translate', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      text,\n      sourceLanguage,\n      targetLanguage\n    })\n  });\n\n  if (!response.ok) {\n    throw new Error('Translation API request failed');\n  }\n\n  const data = await response.json();\n  \n  if (!data.success) {\n    throw new Error(data.error || 'Translation failed');\n  }\n\n  return data.translatedText;\n}\n\nexport const LANGUAGE_NAMES = {\n  en: 'English',\n  si: 'සිංහල'\n} as const;\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,cACpB,IAAY,EACZ,cAA2B,EAC3B,iBAA8B,IAAI;IAElC,0DAA0D;IAC1D,IAAI,mBAAmB,gBAAgB;QACrC,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM,oBAAoB,MAAM,qBAAqB,MAAM,gBAAgB;IAC3E,IAAI,mBAAmB;QACrB,OAAO;IACT;IAEA,IAAI;QACF,kCAAkC;QAClC,MAAM,iBAAiB,MAAM,sBAAsB,MAAM,gBAAgB;QAEzE,wBAAwB;QACxB,MAAM,iBAAiB,MAAM,gBAAgB,gBAAgB;QAE7D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,4CAA4C;QAC5C,OAAO;IACT;AACF;AAEA,eAAe,qBACb,YAAoB,EACpB,cAA2B,EAC3B,cAA2B;IAE3B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,sHAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,mBACP,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,mBAAmB,gBACtB,EAAE,CAAC,mBAAmB,gBACtB,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,OAAO;QACT;QAEA,OAAO,KAAK,eAAe;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAEA,eAAe,iBACb,YAAoB,EACpB,cAAsB,EACtB,cAA2B,EAC3B,cAA2B;IAE3B,IAAI;QACF,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,sHAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC;YACN,eAAe;YACf,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;QACnB;IACJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;IAC9C;AACF;AAEA,eAAe,sBACb,IAAY,EACZ,cAA2B,EAC3B,cAA2B;IAE3B,MAAM,WAAW,MAAM,MAAM,kBAAkB;QAC7C,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB;YACA;YACA;QACF;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;QACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;IAChC;IAEA,OAAO,KAAK,cAAc;AAC5B;AAEO,MAAM,iBAAiB;IAC5B,IAAI;IACJ,IAAI;AACN", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/hooks/useLanguage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport { LanguageContextType } from '@/types';\nimport { translateText } from '@/utils/translation';\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\nexport function LanguageProvider({ children }: { children: React.ReactNode }) {\n  const [language, setLanguage] = useState<'en' | 'si'>('en');\n  const [isTranslating, setIsTranslating] = useState(false);\n\n  const translate = useCallback(async (text: string): Promise<string> => {\n    if (language === 'en') {\n      return text; // No translation needed for English\n    }\n\n    setIsTranslating(true);\n    try {\n      const translatedText = await translateText(text, language, 'en');\n      return translatedText;\n    } catch (error) {\n      console.error('Translation error:', error);\n      return text; // Return original text if translation fails\n    } finally {\n      setIsTranslating(false);\n    }\n  }, [language]);\n\n  const value: LanguageContextType = {\n    language,\n    setLanguage,\n    translate,\n    isTranslating\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n}\n\n// Hook for translating text with caching\nexport function useTranslation() {\n  const { language, translate, isTranslating } = useLanguage();\n  const [translationCache, setTranslationCache] = useState<Map<string, string>>(new Map());\n\n  const t = useCallback(async (text: string): Promise<string> => {\n    if (language === 'en') {\n      return text;\n    }\n\n    const cacheKey = `${text}_${language}`;\n    if (translationCache.has(cacheKey)) {\n      return translationCache.get(cacheKey)!;\n    }\n\n    const translatedText = await translate(text);\n    setTranslationCache(prev => new Map(prev).set(cacheKey, translatedText));\n    return translatedText;\n  }, [language, translate, translationCache]);\n\n  return { t, isTranslating, language };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAJA;;;;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,IAAI,aAAa,MAAM;YACrB,OAAO,MAAM,oCAAoC;QACnD;QAEA,iBAAiB;QACjB,IAAI;YACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,UAAU;YAC3D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,MAAM,4CAA4C;QAC3D,SAAU;YACR,iBAAiB;QACnB;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,QAA6B;QACjC;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,IAAI;IAElF,MAAM,IAAI,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3B,IAAI,aAAa,MAAM;YACrB,OAAO;QACT;QAEA,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,UAAU;QACtC,IAAI,iBAAiB,GAAG,CAAC,WAAW;YAClC,OAAO,iBAAiB,GAAG,CAAC;QAC9B;QAEA,MAAM,iBAAiB,MAAM,UAAU;QACvC,oBAAoB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC,UAAU;QACxD,OAAO;IACT,GAAG;QAAC;QAAU;QAAW;KAAiB;IAE1C,OAAO;QAAE;QAAG;QAAe;IAAS;AACtC", "debugId": null}}]}