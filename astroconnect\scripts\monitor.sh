#!/bin/bash

# AstroConnect Monitoring Script
# This script monitors the health and performance of the AstroConnect application

set -e

# Configuration
APP_URL="${NEXT_PUBLIC_APP_URL:-http://localhost:3000}"
HEALTH_ENDPOINT="$APP_URL/api/health"
LOG_FILE="./monitor.log"
ALERT_EMAIL="${ALERT_EMAIL:-<EMAIL>}"
CHECK_INTERVAL="${CHECK_INTERVAL:-300}" # 5 minutes
MAX_RESPONSE_TIME="${MAX_RESPONSE_TIME:-5000}" # 5 seconds in milliseconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Send alert (placeholder - implement with your preferred method)
send_alert() {
    local subject="$1"
    local message="$2"
    
    log "ALERT: $subject - $message"
    
    # Uncomment and configure your preferred alerting method:
    # echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
    # curl -X POST "https://hooks.slack.com/your-webhook" -d "{\"text\":\"$subject: $message\"}"
    # curl -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" -d "chat_id=$CHAT_ID&text=$subject: $message"
}

# Check application health
check_health() {
    local start_time=$(date +%s%3N)
    local response=$(curl -s -w "%{http_code},%{time_total}" "$HEALTH_ENDPOINT" 2>/dev/null || echo "000,0")
    local end_time=$(date +%s%3N)
    
    local http_code=$(echo "$response" | tail -1 | cut -d',' -f1)
    local response_time=$(echo "$response" | tail -1 | cut -d',' -f2)
    local response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d'.' -f1)
    local body=$(echo "$response" | head -n -1)
    
    # Check HTTP status
    if [ "$http_code" != "200" ]; then
        error "Health check failed - HTTP $http_code"
        send_alert "AstroConnect Health Check Failed" "HTTP Status: $http_code, URL: $HEALTH_ENDPOINT"
        return 1
    fi
    
    # Check response time
    if [ "$response_time_ms" -gt "$MAX_RESPONSE_TIME" ]; then
        warning "Slow response time: ${response_time_ms}ms (threshold: ${MAX_RESPONSE_TIME}ms)"
        send_alert "AstroConnect Slow Response" "Response time: ${response_time_ms}ms, Threshold: ${MAX_RESPONSE_TIME}ms"
    fi
    
    # Parse health response
    if echo "$body" | grep -q '"status":"healthy"'; then
        success "Health check passed (${response_time_ms}ms)"
        
        # Extract additional metrics
        local uptime=$(echo "$body" | grep -o '"uptime":[0-9]*' | cut -d':' -f2)
        local version=$(echo "$body" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
        
        log "Uptime: ${uptime}s, Version: $version"
        return 0
    else
        error "Health check returned unhealthy status"
        send_alert "AstroConnect Unhealthy" "Health endpoint returned unhealthy status: $body"
        return 1
    fi
}

# Check Docker containers
check_containers() {
    if command -v docker &> /dev/null; then
        local containers=$(docker ps --filter "name=astroconnect" --format "table {{.Names}}\t{{.Status}}")
        
        if [ -z "$containers" ] || [ "$containers" = "NAMES	STATUS" ]; then
            error "No AstroConnect containers running"
            send_alert "AstroConnect Containers Down" "No containers found running"
            return 1
        else
            success "Containers running: $(echo "$containers" | tail -n +2 | wc -l)"
            log "$containers"
            return 0
        fi
    else
        log "Docker not available - skipping container check"
        return 0
    fi
}

# Check disk space
check_disk_space() {
    local usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    local threshold=85
    
    if [ "$usage" -gt "$threshold" ]; then
        warning "Disk usage high: ${usage}% (threshold: ${threshold}%)"
        send_alert "AstroConnect Disk Space Warning" "Disk usage: ${usage}%, Threshold: ${threshold}%"
        return 1
    else
        success "Disk usage normal: ${usage}%"
        return 0
    fi
}

# Check memory usage
check_memory() {
    if command -v free &> /dev/null; then
        local mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
        local threshold=85
        
        if [ "$mem_usage" -gt "$threshold" ]; then
            warning "Memory usage high: ${mem_usage}% (threshold: ${threshold}%)"
            send_alert "AstroConnect Memory Warning" "Memory usage: ${mem_usage}%, Threshold: ${threshold}%"
            return 1
        else
            success "Memory usage normal: ${mem_usage}%"
            return 0
        fi
    else
        log "Memory check not available on this system"
        return 0
    fi
}

# Check SSL certificate expiry
check_ssl_cert() {
    if [[ "$APP_URL" == https://* ]]; then
        local domain=$(echo "$APP_URL" | sed 's|https://||' | cut -d'/' -f1)
        local expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -enddate 2>/dev/null | cut -d= -f2)
        
        if [ -n "$expiry_date" ]; then
            local expiry_epoch=$(date -d "$expiry_date" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$expiry_date" +%s 2>/dev/null)
            local current_epoch=$(date +%s)
            local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
            
            if [ "$days_until_expiry" -lt 30 ]; then
                warning "SSL certificate expires in $days_until_expiry days"
                send_alert "AstroConnect SSL Certificate Expiry" "Certificate expires in $days_until_expiry days"
                return 1
            else
                success "SSL certificate valid for $days_until_expiry days"
                return 0
            fi
        else
            warning "Could not check SSL certificate"
            return 1
        fi
    else
        log "HTTP URL - skipping SSL check"
        return 0
    fi
}

# Generate monitoring report
generate_report() {
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    local report_file="./monitoring-report-$(date +'%Y%m%d').json"
    
    cat > "$report_file" << EOF
{
  "timestamp": "$timestamp",
  "app_url": "$APP_URL",
  "checks": {
    "health": $(check_health &>/dev/null && echo "true" || echo "false"),
    "containers": $(check_containers &>/dev/null && echo "true" || echo "false"),
    "disk_space": $(check_disk_space &>/dev/null && echo "true" || echo "false"),
    "memory": $(check_memory &>/dev/null && echo "true" || echo "false"),
    "ssl_cert": $(check_ssl_cert &>/dev/null && echo "true" || echo "false")
  }
}
EOF
    
    log "Monitoring report generated: $report_file"
}

# Main monitoring function
run_monitoring() {
    log "Starting monitoring check for $APP_URL"
    
    local failed_checks=0
    
    # Run all checks
    check_health || ((failed_checks++))
    check_containers || ((failed_checks++))
    check_disk_space || ((failed_checks++))
    check_memory || ((failed_checks++))
    check_ssl_cert || ((failed_checks++))
    
    # Generate report
    generate_report
    
    if [ "$failed_checks" -eq 0 ]; then
        success "All monitoring checks passed"
    else
        error "$failed_checks monitoring check(s) failed"
    fi
    
    log "Monitoring check completed"
    return $failed_checks
}

# Continuous monitoring
continuous_monitoring() {
    log "Starting continuous monitoring (interval: ${CHECK_INTERVAL}s)"
    
    while true; do
        run_monitoring
        log "Waiting ${CHECK_INTERVAL} seconds until next check..."
        sleep "$CHECK_INTERVAL"
    done
}

# Main script
main() {
    case "${1:-check}" in
        "check")
            run_monitoring
            ;;
        "continuous")
            continuous_monitoring
            ;;
        "report")
            generate_report
            ;;
        *)
            echo "Usage: $0 {check|continuous|report}"
            echo "  check      - Run monitoring checks once (default)"
            echo "  continuous - Run monitoring checks continuously"
            echo "  report     - Generate monitoring report only"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
