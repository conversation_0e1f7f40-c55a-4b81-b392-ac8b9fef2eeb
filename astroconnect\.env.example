# AstroConnect Environment Configuration
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Get these from your Supabase project settings
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# GEMINI API CONFIGURATION
# =============================================================================
# Get your API key from Google AI Studio
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Your application's public URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Environment (development, production, test)
NODE_ENV=development

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Admin Configuration (for production)
# ADMIN_EMAIL=<EMAIL>
# ADMIN_PASSWORD_HASH=your_hashed_password

# Rate Limiting (requests per minute)
# RATE_LIMIT_API=60
# RATE_LIMIT_AUTH=10
# RATE_LIMIT_TRANSLATE=50

# Security Configuration
# JWT_SECRET=your_jwt_secret_for_sessions
# ENCRYPTION_KEY=your_encryption_key_for_sensitive_data

# Monitoring and Analytics
# SENTRY_DSN=your_sentry_dsn_for_error_tracking
# GOOGLE_ANALYTICS_ID=your_google_analytics_id

# Email Configuration (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password

# =============================================================================
# DEVELOPMENT ONLY
# =============================================================================
# These are only used in development mode

# Debug logging
# DEBUG=true
# LOG_LEVEL=debug

# Development database (if using local PostgreSQL)
# DATABASE_URL=postgresql://username:password@localhost:5432/astroconnect

# =============================================================================
# PRODUCTION ONLY
# =============================================================================
# These are only used in production mode

# SSL Configuration
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/key.pem

# CDN Configuration
# CDN_URL=https://cdn.yourdomain.com

# Backup Configuration
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
# These are used when running with Docker

# Container configuration
# CONTAINER_PORT=3000
# CONTAINER_NAME=astroconnect-app

# Docker network
# DOCKER_NETWORK=astroconnect-network

# =============================================================================
# NOTES
# =============================================================================
# 
# 1. Never commit .env.local to version control
# 2. Use strong, unique values for all secrets
# 3. Rotate API keys and secrets regularly
# 4. Use environment-specific values for different deployments
# 5. Validate all environment variables on application startup
#
# For more information, see the deployment documentation:
# https://github.com/your-repo/astroconnect/blob/main/DEPLOYMENT.md
