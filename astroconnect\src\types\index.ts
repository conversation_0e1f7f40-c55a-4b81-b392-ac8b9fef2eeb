// User Types
export interface User {
  id: string;
  email?: string;
  name: string;
  zodiac_sign: ZodiacSign;
  birth_date: string;
  qr_token: string;
  created_at: string;
  updated_at: string;
  language_preference: 'en' | 'si';
}

// Zodiac Signs
export type ZodiacSign = 
  | 'aries' | 'taurus' | 'gemini' | 'cancer' 
  | 'leo' | 'virgo' | 'libra' | 'scorpio' 
  | 'sagittarius' | 'capricorn' | 'aquarius' | 'pisces';

// Horoscope Types
export interface Horoscope {
  id: string;
  zodiac_sign: ZodiacSign;
  type: 'daily' | 'weekly' | 'monthly';
  content: string;
  date: string;
  created_at: string;
  language: 'en' | 'si';
}

// Daily Guide Types
export interface DailyGuide {
  id: string;
  zodiac_sign: ZodiacSign;
  date: string;
  lucky_number: number;
  lucky_color: string;
  lucky_time: string;
  advice: string;
  created_at: string;
  language: 'en' | 'si';
}

// QR Code Mapping
export interface QRCodeMapping {
  id: string;
  qr_token: string;
  user_id: string;
  created_at: string;
  last_scanned: string | null;
  scan_count: number;
}

// Translation Cache
export interface TranslationCache {
  id: string;
  original_text: string;
  translated_text: string;
  source_language: 'en' | 'si';
  target_language: 'en' | 'si';
  created_at: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Dashboard Data
export interface DashboardData {
  user: User;
  todayHoroscope: Horoscope;
  weeklyHoroscope: Horoscope;
  monthlyHoroscope: Horoscope;
  dailyGuide: DailyGuide;
}

// Language Context
export interface LanguageContextType {
  language: 'en' | 'si';
  setLanguage: (lang: 'en' | 'si') => void;
  translate: (text: string) => Promise<string>;
  isTranslating: boolean;
}

// QR Scanner Props
export interface QRScannerProps {
  onScanSuccess: (decodedText: string) => void;
  onScanError?: (error: string) => void;
  width?: number;
  height?: number;
}
