import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Client for browser/frontend use
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Admin client for server-side operations
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Database Tables
export const TABLES = {
  USERS: 'users',
  HOROSCOPES: 'horoscopes',
  DAILY_GUIDES: 'daily_guides',
  QR_CODE_MAPPINGS: 'qr_code_mappings',
  TRANSLATION_CACHE: 'translation_cache'
} as const;
