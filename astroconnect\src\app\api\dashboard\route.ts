import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin, TABLES } from '@/lib/supabase';
import { ApiResponse, DashboardData, User, Horoscope, DailyGuide } from '@/types';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');
  const language = searchParams.get('language') as 'en' | 'si' || 'en';

  if (!userId) {
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'User ID is required'
    }, { status: 400 });
  }

  try {
    // Get user data
    const { data: user, error: userError } = await supabaseAdmin
      .from(TABLES.USERS)
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    const today = new Date().toISOString().split('T')[0];
    const zodiacSign = user.zodiac_sign;

    // Get horoscopes for user's zodiac sign
    const { data: horoscopes, error: horoscopeError } = await supabaseAdmin
      .from(TABLES.HOROSCOPES)
      .select('*')
      .eq('zodiac_sign', zodiacSign)
      .eq('date', today)
      .eq('language', language);

    if (horoscopeError) {
      console.error('Error fetching horoscopes:', horoscopeError);
    }

    // Get daily guide for user's zodiac sign
    const { data: dailyGuide, error: guideError } = await supabaseAdmin
      .from(TABLES.DAILY_GUIDES)
      .select('*')
      .eq('zodiac_sign', zodiacSign)
      .eq('date', today)
      .eq('language', language)
      .single();

    if (guideError) {
      console.error('Error fetching daily guide:', guideError);
    }

    // Organize horoscopes by type
    const todayHoroscope = horoscopes?.find(h => h.type === 'daily') || null;
    const weeklyHoroscope = horoscopes?.find(h => h.type === 'weekly') || null;
    const monthlyHoroscope = horoscopes?.find(h => h.type === 'monthly') || null;

    const dashboardData: DashboardData = {
      user: user as User,
      todayHoroscope: todayHoroscope as Horoscope,
      weeklyHoroscope: weeklyHoroscope as Horoscope,
      monthlyHoroscope: monthlyHoroscope as Horoscope,
      dailyGuide: dailyGuide as DailyGuide
    };

    return NextResponse.json<ApiResponse<DashboardData>>({
      success: true,
      data: dashboardData,
      message: 'Dashboard data retrieved successfully'
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, language } = await request.json();

    if (!userId) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    // Update user's language preference
    const { error } = await supabaseAdmin
      .from(TABLES.USERS)
      .update({ language_preference: language })
      .eq('id', userId);

    if (error) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Failed to update language preference'
      }, { status: 500 });
    }

    return NextResponse.json<ApiResponse<{ updated: boolean }>>({
      success: true,
      data: { updated: true },
      message: 'Language preference updated successfully'
    });

  } catch (error) {
    console.error('Language update error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
