import { Runtime } from '../parser/index.mjs';
import * as S from './mapping.mjs';
const If = (result, left, right = () => []) => (result.length === 2 ? left(result) : right());
export const GenericReferenceParameterList_0 = (input, context, result = []) => If(If(Type(input, context), ([_0, input]) => If(Runtime.Token.Const(',', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => GenericReferenceParameterList_0(input, context, [...result, _0]), () => [result, input]);
export const GenericReferenceParameterList = (input, context = {}) => If(If(GenericReferenceParameterList_0(input, context), ([_0, input]) => If(If(If(Type(input, context), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.GenericReferenceParameterListMapping(_0, context), input]);
export const GenericReference = (input, context = {}) => If(If(Runtime.Token.Ident(input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(GenericReferenceParameterList(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.GenericReferenceMapping(_0, context), input]);
export const GenericArgumentsList_0 = (input, context, result = []) => If(If(Runtime.Token.Ident(input), ([_0, input]) => If(Runtime.Token.Const(',', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => GenericArgumentsList_0(input, context, [...result, _0]), () => [result, input]);
export const GenericArgumentsList = (input, context = {}) => If(If(GenericArgumentsList_0(input, context), ([_0, input]) => If(If(If(Runtime.Token.Ident(input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.GenericArgumentsListMapping(_0, context), input]);
export const GenericArguments = (input, context = {}) => If(If(Runtime.Token.Const('<', input), ([_0, input]) => If(GenericArgumentsList(input, context), ([_1, input]) => If(Runtime.Token.Const('>', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [S.GenericArgumentsMapping(_0, context), input]);
export const KeywordString = (input, context = {}) => If(Runtime.Token.Const('string', input), ([_0, input]) => [S.KeywordStringMapping(_0, context), input]);
export const KeywordNumber = (input, context = {}) => If(Runtime.Token.Const('number', input), ([_0, input]) => [S.KeywordNumberMapping(_0, context), input]);
export const KeywordBoolean = (input, context = {}) => If(Runtime.Token.Const('boolean', input), ([_0, input]) => [S.KeywordBooleanMapping(_0, context), input]);
export const KeywordUndefined = (input, context = {}) => If(Runtime.Token.Const('undefined', input), ([_0, input]) => [S.KeywordUndefinedMapping(_0, context), input]);
export const KeywordNull = (input, context = {}) => If(Runtime.Token.Const('null', input), ([_0, input]) => [S.KeywordNullMapping(_0, context), input]);
export const KeywordInteger = (input, context = {}) => If(Runtime.Token.Const('integer', input), ([_0, input]) => [S.KeywordIntegerMapping(_0, context), input]);
export const KeywordBigInt = (input, context = {}) => If(Runtime.Token.Const('bigint', input), ([_0, input]) => [S.KeywordBigIntMapping(_0, context), input]);
export const KeywordUnknown = (input, context = {}) => If(Runtime.Token.Const('unknown', input), ([_0, input]) => [S.KeywordUnknownMapping(_0, context), input]);
export const KeywordAny = (input, context = {}) => If(Runtime.Token.Const('any', input), ([_0, input]) => [S.KeywordAnyMapping(_0, context), input]);
export const KeywordNever = (input, context = {}) => If(Runtime.Token.Const('never', input), ([_0, input]) => [S.KeywordNeverMapping(_0, context), input]);
export const KeywordSymbol = (input, context = {}) => If(Runtime.Token.Const('symbol', input), ([_0, input]) => [S.KeywordSymbolMapping(_0, context), input]);
export const KeywordVoid = (input, context = {}) => If(Runtime.Token.Const('void', input), ([_0, input]) => [S.KeywordVoidMapping(_0, context), input]);
export const Keyword = (input, context = {}) => If(If(KeywordString(input, context), ([_0, input]) => [_0, input], () => If(KeywordNumber(input, context), ([_0, input]) => [_0, input], () => If(KeywordBoolean(input, context), ([_0, input]) => [_0, input], () => If(KeywordUndefined(input, context), ([_0, input]) => [_0, input], () => If(KeywordNull(input, context), ([_0, input]) => [_0, input], () => If(KeywordInteger(input, context), ([_0, input]) => [_0, input], () => If(KeywordBigInt(input, context), ([_0, input]) => [_0, input], () => If(KeywordUnknown(input, context), ([_0, input]) => [_0, input], () => If(KeywordAny(input, context), ([_0, input]) => [_0, input], () => If(KeywordNever(input, context), ([_0, input]) => [_0, input], () => If(KeywordSymbol(input, context), ([_0, input]) => [_0, input], () => If(KeywordVoid(input, context), ([_0, input]) => [_0, input], () => [])))))))))))), ([_0, input]) => [S.KeywordMapping(_0, context), input]);
export const LiteralString = (input, context = {}) => If(Runtime.Token.String(["'", '"', '`'], input), ([_0, input]) => [S.LiteralStringMapping(_0, context), input]);
export const LiteralNumber = (input, context = {}) => If(Runtime.Token.Number(input), ([_0, input]) => [S.LiteralNumberMapping(_0, context), input]);
export const LiteralBoolean = (input, context = {}) => If(If(Runtime.Token.Const('true', input), ([_0, input]) => [_0, input], () => If(Runtime.Token.Const('false', input), ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.LiteralBooleanMapping(_0, context), input]);
export const Literal = (input, context = {}) => If(If(LiteralBoolean(input, context), ([_0, input]) => [_0, input], () => If(LiteralNumber(input, context), ([_0, input]) => [_0, input], () => If(LiteralString(input, context), ([_0, input]) => [_0, input], () => []))), ([_0, input]) => [S.LiteralMapping(_0, context), input]);
export const KeyOf = (input, context = {}) => If(If(If(Runtime.Token.Const('keyof', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.KeyOfMapping(_0, context), input]);
export const IndexArray_0 = (input, context, result = []) => If(If(If(Runtime.Token.Const('[', input), ([_0, input]) => If(Type(input, context), ([_1, input]) => If(Runtime.Token.Const(']', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [_0, input], () => If(If(Runtime.Token.Const('[', input), ([_0, input]) => If(Runtime.Token.Const(']', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [_0, input], () => [])), ([_0, input]) => IndexArray_0(input, context, [...result, _0]), () => [result, input]);
export const IndexArray = (input, context = {}) => If(IndexArray_0(input, context), ([_0, input]) => [S.IndexArrayMapping(_0, context), input]);
export const Extends = (input, context = {}) => If(If(If(Runtime.Token.Const('extends', input), ([_0, input]) => If(Type(input, context), ([_1, input]) => If(Runtime.Token.Const('?', input), ([_2, input]) => If(Type(input, context), ([_3, input]) => If(Runtime.Token.Const(':', input), ([_4, input]) => If(Type(input, context), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.ExtendsMapping(_0, context), input]);
export const Base = (input, context = {}) => If(If(If(Runtime.Token.Const('(', input), ([_0, input]) => If(Type(input, context), ([_1, input]) => If(Runtime.Token.Const(')', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [_0, input], () => If(Keyword(input, context), ([_0, input]) => [_0, input], () => If(_Object(input, context), ([_0, input]) => [_0, input], () => If(Tuple(input, context), ([_0, input]) => [_0, input], () => If(Literal(input, context), ([_0, input]) => [_0, input], () => If(Constructor(input, context), ([_0, input]) => [_0, input], () => If(Function(input, context), ([_0, input]) => [_0, input], () => If(Mapped(input, context), ([_0, input]) => [_0, input], () => If(AsyncIterator(input, context), ([_0, input]) => [_0, input], () => If(Iterator(input, context), ([_0, input]) => [_0, input], () => If(ConstructorParameters(input, context), ([_0, input]) => [_0, input], () => If(FunctionParameters(input, context), ([_0, input]) => [_0, input], () => If(InstanceType(input, context), ([_0, input]) => [_0, input], () => If(ReturnType(input, context), ([_0, input]) => [_0, input], () => If(Argument(input, context), ([_0, input]) => [_0, input], () => If(Awaited(input, context), ([_0, input]) => [_0, input], () => If(Array(input, context), ([_0, input]) => [_0, input], () => If(Record(input, context), ([_0, input]) => [_0, input], () => If(Promise(input, context), ([_0, input]) => [_0, input], () => If(Partial(input, context), ([_0, input]) => [_0, input], () => If(Required(input, context), ([_0, input]) => [_0, input], () => If(Pick(input, context), ([_0, input]) => [_0, input], () => If(Omit(input, context), ([_0, input]) => [_0, input], () => If(Exclude(input, context), ([_0, input]) => [_0, input], () => If(Extract(input, context), ([_0, input]) => [_0, input], () => If(Uppercase(input, context), ([_0, input]) => [_0, input], () => If(Lowercase(input, context), ([_0, input]) => [_0, input], () => If(Capitalize(input, context), ([_0, input]) => [_0, input], () => If(Uncapitalize(input, context), ([_0, input]) => [_0, input], () => If(Date(input, context), ([_0, input]) => [_0, input], () => If(Uint8Array(input, context), ([_0, input]) => [_0, input], () => If(GenericReference(input, context), ([_0, input]) => [_0, input], () => If(Reference(input, context), ([_0, input]) => [_0, input], () => []))))))))))))))))))))))))))))))))), ([_0, input]) => [S.BaseMapping(_0, context), input]);
export const Factor = (input, context = {}) => If(If(KeyOf(input, context), ([_0, input]) => If(Base(input, context), ([_1, input]) => If(IndexArray(input, context), ([_2, input]) => If(Extends(input, context), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.FactorMapping(_0, context), input]);
export const ExprTermTail = (input, context = {}) => If(If(If(Runtime.Token.Const('&', input), ([_0, input]) => If(Factor(input, context), ([_1, input]) => If(ExprTermTail(input, context), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.ExprTermTailMapping(_0, context), input]);
export const ExprTerm = (input, context = {}) => If(If(Factor(input, context), ([_0, input]) => If(ExprTermTail(input, context), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.ExprTermMapping(_0, context), input]);
export const ExprTail = (input, context = {}) => If(If(If(Runtime.Token.Const('|', input), ([_0, input]) => If(ExprTerm(input, context), ([_1, input]) => If(ExprTail(input, context), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.ExprTailMapping(_0, context), input]);
export const Expr = (input, context = {}) => If(If(ExprTerm(input, context), ([_0, input]) => If(ExprTail(input, context), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.ExprMapping(_0, context), input]);
export const Type = (input, context = {}) => If(If(If(GenericArguments(input, context), ([_0, input]) => Expr(input, _0), () => []), ([_0, input]) => [_0, input], () => If(Expr(input, context), ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.TypeMapping(_0, context), input]);
export const PropertyKey = (input, context = {}) => If(If(Runtime.Token.Ident(input), ([_0, input]) => [_0, input], () => If(Runtime.Token.String(["'", '"'], input), ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.PropertyKeyMapping(_0, context), input]);
export const Readonly = (input, context = {}) => If(If(If(Runtime.Token.Const('readonly', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.ReadonlyMapping(_0, context), input]);
export const Optional = (input, context = {}) => If(If(If(Runtime.Token.Const('?', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.OptionalMapping(_0, context), input]);
export const Property = (input, context = {}) => If(If(Readonly(input, context), ([_0, input]) => If(PropertyKey(input, context), ([_1, input]) => If(Optional(input, context), ([_2, input]) => If(Runtime.Token.Const(':', input), ([_3, input]) => If(Type(input, context), ([_4, input]) => [[_0, _1, _2, _3, _4], input]))))), ([_0, input]) => [S.PropertyMapping(_0, context), input]);
export const PropertyDelimiter = (input, context = {}) => If(If(If(Runtime.Token.Const(',', input), ([_0, input]) => If(Runtime.Token.Const('\n', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [_0, input], () => If(If(Runtime.Token.Const(';', input), ([_0, input]) => If(Runtime.Token.Const('\n', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [_0, input], () => If(If(Runtime.Token.Const(',', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If(If(Runtime.Token.Const(';', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If(If(Runtime.Token.Const('\n', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => []))))), ([_0, input]) => [S.PropertyDelimiterMapping(_0, context), input]);
export const PropertyList_0 = (input, context, result = []) => If(If(Property(input, context), ([_0, input]) => If(PropertyDelimiter(input, context), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => PropertyList_0(input, context, [...result, _0]), () => [result, input]);
export const PropertyList = (input, context = {}) => If(If(PropertyList_0(input, context), ([_0, input]) => If(If(If(Property(input, context), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.PropertyListMapping(_0, context), input]);
export const _Object = (input, context = {}) => If(If(Runtime.Token.Const('{', input), ([_0, input]) => If(PropertyList(input, context), ([_1, input]) => If(Runtime.Token.Const('}', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [S.ObjectMapping(_0, context), input]);
export const ElementList_0 = (input, context, result = []) => If(If(Type(input, context), ([_0, input]) => If(Runtime.Token.Const(',', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => ElementList_0(input, context, [...result, _0]), () => [result, input]);
export const ElementList = (input, context = {}) => If(If(ElementList_0(input, context), ([_0, input]) => If(If(If(Type(input, context), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.ElementListMapping(_0, context), input]);
export const Tuple = (input, context = {}) => If(If(Runtime.Token.Const('[', input), ([_0, input]) => If(ElementList(input, context), ([_1, input]) => If(Runtime.Token.Const(']', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [S.TupleMapping(_0, context), input]);
export const Parameter = (input, context = {}) => If(If(Runtime.Token.Ident(input), ([_0, input]) => If(Runtime.Token.Const(':', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [S.ParameterMapping(_0, context), input]);
export const ParameterList_0 = (input, context, result = []) => If(If(Parameter(input, context), ([_0, input]) => If(Runtime.Token.Const(',', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => ParameterList_0(input, context, [...result, _0]), () => [result, input]);
export const ParameterList = (input, context = {}) => If(If(ParameterList_0(input, context), ([_0, input]) => If(If(If(Parameter(input, context), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.ParameterListMapping(_0, context), input]);
export const Function = (input, context = {}) => If(If(Runtime.Token.Const('(', input), ([_0, input]) => If(ParameterList(input, context), ([_1, input]) => If(Runtime.Token.Const(')', input), ([_2, input]) => If(Runtime.Token.Const('=>', input), ([_3, input]) => If(Type(input, context), ([_4, input]) => [[_0, _1, _2, _3, _4], input]))))), ([_0, input]) => [S.FunctionMapping(_0, context), input]);
export const Constructor = (input, context = {}) => If(If(Runtime.Token.Const('new', input), ([_0, input]) => If(Runtime.Token.Const('(', input), ([_1, input]) => If(ParameterList(input, context), ([_2, input]) => If(Runtime.Token.Const(')', input), ([_3, input]) => If(Runtime.Token.Const('=>', input), ([_4, input]) => If(Type(input, context), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.ConstructorMapping(_0, context), input]);
export const Mapped = (input, context = {}) => If(If(Runtime.Token.Const('{', input), ([_0, input]) => If(Runtime.Token.Const('[', input), ([_1, input]) => If(Runtime.Token.Ident(input), ([_2, input]) => If(Runtime.Token.Const('in', input), ([_3, input]) => If(Type(input, context), ([_4, input]) => If(Runtime.Token.Const(']', input), ([_5, input]) => If(Runtime.Token.Const(':', input), ([_6, input]) => If(Type(input, context), ([_7, input]) => If(Runtime.Token.Const('}', input), ([_8, input]) => [[_0, _1, _2, _3, _4, _5, _6, _7, _8], input]))))))))), ([_0, input]) => [S.MappedMapping(_0, context), input]);
export const AsyncIterator = (input, context = {}) => If(If(Runtime.Token.Const('AsyncIterator', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.AsyncIteratorMapping(_0, context), input]);
export const Iterator = (input, context = {}) => If(If(Runtime.Token.Const('Iterator', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.IteratorMapping(_0, context), input]);
export const Argument = (input, context = {}) => If(If(Runtime.Token.Const('Argument', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.ArgumentMapping(_0, context), input]);
export const Awaited = (input, context = {}) => If(If(Runtime.Token.Const('Awaited', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.AwaitedMapping(_0, context), input]);
export const Array = (input, context = {}) => If(If(Runtime.Token.Const('Array', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.ArrayMapping(_0, context), input]);
export const Record = (input, context = {}) => If(If(Runtime.Token.Const('Record', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const(',', input), ([_3, input]) => If(Type(input, context), ([_4, input]) => If(Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.RecordMapping(_0, context), input]);
export const Promise = (input, context = {}) => If(If(Runtime.Token.Const('Promise', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.PromiseMapping(_0, context), input]);
export const ConstructorParameters = (input, context = {}) => If(If(Runtime.Token.Const('ConstructorParameters', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.ConstructorParametersMapping(_0, context), input]);
export const FunctionParameters = (input, context = {}) => If(If(Runtime.Token.Const('Parameters', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.FunctionParametersMapping(_0, context), input]);
export const InstanceType = (input, context = {}) => If(If(Runtime.Token.Const('InstanceType', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.InstanceTypeMapping(_0, context), input]);
export const ReturnType = (input, context = {}) => If(If(Runtime.Token.Const('ReturnType', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.ReturnTypeMapping(_0, context), input]);
export const Partial = (input, context = {}) => If(If(Runtime.Token.Const('Partial', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.PartialMapping(_0, context), input]);
export const Required = (input, context = {}) => If(If(Runtime.Token.Const('Required', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.RequiredMapping(_0, context), input]);
export const Pick = (input, context = {}) => If(If(Runtime.Token.Const('Pick', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const(',', input), ([_3, input]) => If(Type(input, context), ([_4, input]) => If(Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.PickMapping(_0, context), input]);
export const Omit = (input, context = {}) => If(If(Runtime.Token.Const('Omit', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const(',', input), ([_3, input]) => If(Type(input, context), ([_4, input]) => If(Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.OmitMapping(_0, context), input]);
export const Exclude = (input, context = {}) => If(If(Runtime.Token.Const('Exclude', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const(',', input), ([_3, input]) => If(Type(input, context), ([_4, input]) => If(Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.ExcludeMapping(_0, context), input]);
export const Extract = (input, context = {}) => If(If(Runtime.Token.Const('Extract', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const(',', input), ([_3, input]) => If(Type(input, context), ([_4, input]) => If(Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.ExtractMapping(_0, context), input]);
export const Uppercase = (input, context = {}) => If(If(Runtime.Token.Const('Uppercase', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.UppercaseMapping(_0, context), input]);
export const Lowercase = (input, context = {}) => If(If(Runtime.Token.Const('Lowercase', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.LowercaseMapping(_0, context), input]);
export const Capitalize = (input, context = {}) => If(If(Runtime.Token.Const('Capitalize', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.CapitalizeMapping(_0, context), input]);
export const Uncapitalize = (input, context = {}) => If(If(Runtime.Token.Const('Uncapitalize', input), ([_0, input]) => If(Runtime.Token.Const('<', input), ([_1, input]) => If(Type(input, context), ([_2, input]) => If(Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.UncapitalizeMapping(_0, context), input]);
export const Date = (input, context = {}) => If(Runtime.Token.Const('Date', input), ([_0, input]) => [S.DateMapping(_0, context), input]);
export const Uint8Array = (input, context = {}) => If(Runtime.Token.Const('Uint8Array', input), ([_0, input]) => [S.Uint8ArrayMapping(_0, context), input]);
export const Reference = (input, context = {}) => If(Runtime.Token.Ident(input), ([_0, input]) => [S.ReferenceMapping(_0, context), input]);
