import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { hashPassword } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function POST() {
  try {
    // Check if any admin already exists
    const existingAdmin = await prisma.admin.findFirst();
    
    if (existingAdmin) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Admin user already exists'
      }, { status: 400 });
    }

    // Create demo admin user
    const demoEmail = '<EMAIL>';
    const demoPassword = 'admin123'; // In production, this should be more secure
    const hashedPassword = await hashPassword(demoPassword);

    const admin = await prisma.admin.create({
      data: {
        email: demoEmail,
        password: hashedPassword,
        name: 'Demo Admin',
        role: 'admin',
        isActive: true
      }
    });

    return NextResponse.json<ApiResponse<{
      admin: {
        id: string;
        email: string;
        name: string;
      };
      credentials: {
        email: string;
        password: string;
      };
    }>>({
      success: true,
      data: {
        admin: {
          id: admin.id,
          email: admin.email,
          name: admin.name
        },
        credentials: {
          email: demoEmail,
          password: demoPassword
        }
      },
      message: 'Demo admin user created successfully'
    });

  } catch (error) {
    console.error('Admin setup error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
