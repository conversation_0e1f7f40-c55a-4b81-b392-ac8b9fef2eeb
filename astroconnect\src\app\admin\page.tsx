'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Users, FileText, BarChart3, Globe, Plus, Search, Filter, LogOut, Shield } from 'lucide-react';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';

interface AdminStats {
  totalUsers: number;
  totalHoroscopes: number;
  totalScans: number;
  activeUsers: number;
}

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: string;
}

export default function AdminDashboard() {
  const [admin, setAdmin] = useState<AdminUser | null>(null);
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'content' | 'analytics'>('overview');
  const router = useRouter();

  useEffect(() => {
    checkAdminAuth();
  }, []);

  const checkAdminAuth = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      if (!token) {
        router.push('/admin/login');
        return;
      }

      const response = await fetch('/api/admin/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAdmin(data.data.admin);
        await fetchAdminStats();
      } else {
        localStorage.removeItem('admin-token');
        localStorage.removeItem('admin-user');
        router.push('/admin/login');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      router.push('/admin/login');
    }
  };

  const fetchAdminStats = async () => {
    try {
      setLoading(true);
      // This would be replaced with actual API calls
      const mockStats: AdminStats = {
        totalUsers: 1250,
        totalHoroscopes: 365,
        totalScans: 8420,
        activeUsers: 892
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setStats(mockStats);
    } catch (err) {
      setError('Failed to load admin statistics');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth/logout', { method: 'POST' });
      localStorage.removeItem('admin-token');
      localStorage.removeItem('admin-user');
      router.push('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      localStorage.removeItem('admin-token');
      localStorage.removeItem('admin-user');
      router.push('/admin/login');
    }
  };

  if (loading || !admin) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <LoadingSpinner message="Loading admin dashboard..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <ErrorMessage
          title="Admin Dashboard Error"
          message={error}
          onRetry={fetchAdminStats}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">AstroConnect Admin</h1>
              <p className="text-gray-300">Manage users, content, and analytics</p>
            </div>

            <div className="flex items-center space-x-4">
              {admin && (
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-white font-medium">{admin.name}</p>
                    <p className="text-gray-300 text-sm">{admin.email}</p>
                  </div>
                  <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <LogOut size={16} />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-black/10 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'users', label: 'Users', icon: Users },
              { id: 'content', label: 'Content', icon: FileText },
              { id: 'analytics', label: 'Analytics', icon: BarChart3 }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ${
                  activeTab === id
                    ? 'border-purple-400 text-white'
                    : 'border-transparent text-gray-300 hover:text-white'
                }`}
              >
                <Icon size={16} />
                <span>{label}</span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {activeTab === 'overview' && stats && (
          <div className="space-y-8">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-300 text-sm">Total Users</p>
                    <p className="text-3xl font-bold text-white">{stats.totalUsers.toLocaleString()}</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-300 text-sm">Active Users</p>
                    <p className="text-3xl font-bold text-white">{stats.activeUsers.toLocaleString()}</p>
                  </div>
                  <Users className="w-8 h-8 text-green-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-300 text-sm">Total Horoscopes</p>
                    <p className="text-3xl font-bold text-white">{stats.totalHoroscopes.toLocaleString()}</p>
                  </div>
                  <FileText className="w-8 h-8 text-purple-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-300 text-sm">QR Scans</p>
                    <p className="text-3xl font-bold text-white">{stats.totalScans.toLocaleString()}</p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-yellow-400" />
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4">Recent Activity</h2>
              <div className="space-y-4">
                {[
                  { action: 'New user registered', user: '<EMAIL>', time: '2 minutes ago' },
                  { action: 'QR code scanned', user: '<EMAIL>', time: '5 minutes ago' },
                  { action: 'Horoscope updated', user: 'Admin', time: '1 hour ago' },
                  { action: 'New user registered', user: '<EMAIL>', time: '2 hours ago' }
                ].map((activity, index) => (
                  <div key={index} className="flex items-center justify-between py-2 border-b border-white/10 last:border-b-0">
                    <div>
                      <p className="text-white">{activity.action}</p>
                      <p className="text-gray-400 text-sm">{activity.user}</p>
                    </div>
                    <p className="text-gray-400 text-sm">{activity.time}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search users..."
                  className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              <button className="flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors">
                <Filter size={16} />
                <span>Filter</span>
              </button>
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                <Plus size={16} />
                <span>Add User</span>
              </button>
            </div>

            {/* Users Table */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-white/5">
                    <tr>
                      <th className="text-left py-3 px-4 text-gray-300 font-semibold">Name</th>
                      <th className="text-left py-3 px-4 text-gray-300 font-semibold">Email</th>
                      <th className="text-left py-3 px-4 text-gray-300 font-semibold">Zodiac</th>
                      <th className="text-left py-3 px-4 text-gray-300 font-semibold">Scans</th>
                      <th className="text-left py-3 px-4 text-gray-300 font-semibold">Last Active</th>
                      <th className="text-left py-3 px-4 text-gray-300 font-semibold">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { name: 'John Doe', email: '<EMAIL>', zodiac: 'Aries', scans: 15, lastActive: '2 hours ago' },
                      { name: 'Jane Smith', email: '<EMAIL>', zodiac: 'Leo', scans: 23, lastActive: '1 day ago' },
                      { name: 'Kasun Perera', email: '<EMAIL>', zodiac: 'Scorpio', scans: 8, lastActive: '3 days ago' }
                    ].map((user, index) => (
                      <tr key={index} className="border-t border-white/10 hover:bg-white/5">
                        <td className="py-3 px-4 text-white">{user.name}</td>
                        <td className="py-3 px-4 text-gray-300">{user.email}</td>
                        <td className="py-3 px-4 text-gray-300">{user.zodiac}</td>
                        <td className="py-3 px-4 text-gray-300">{user.scans}</td>
                        <td className="py-3 px-4 text-gray-300">{user.lastActive}</td>
                        <td className="py-3 px-4">
                          <button className="text-purple-400 hover:text-purple-300 text-sm">Edit</button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {(activeTab === 'content' || activeTab === 'analytics') && (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
            <h2 className="text-xl font-bold text-white mb-4">
              {activeTab === 'content' ? 'Content Management' : 'Analytics Dashboard'}
            </h2>
            <p className="text-gray-300 mb-6">
              {activeTab === 'content' 
                ? 'Manage horoscopes, daily guides, and translations'
                : 'View detailed analytics and user insights'
              }
            </p>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
              Coming Soon
            </button>
          </div>
        )}
      </main>
    </div>
  );
}
