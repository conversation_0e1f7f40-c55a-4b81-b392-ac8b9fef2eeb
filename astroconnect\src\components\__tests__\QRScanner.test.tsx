import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import QRScanner from '../QRScanner';

// Mock html5-qrcode
const mockHtml5QrcodeScanner = {
  render: jest.fn(),
  clear: jest.fn().mockResolvedValue(undefined),
};

const mockHtml5Qrcode = {
  scanFile: jest.fn(),
};

jest.mock('html5-qrcode', () => ({
  Html5QrcodeScanner: jest.fn().mockImplementation(() => mockHtml5QrcodeScanner),
  Html5Qrcode: jest.fn().mockImplementation(() => mockHtml5Qrcode),
}));

// Mock window.Html5Qrcode for file scanning
Object.defineProperty(window, 'Html5Qrcode', {
  value: jest.fn().mockImplementation(() => mockHtml5Qrcode),
  writable: true,
});

describe('QRScanner Component', () => {
  const mockOnScanSuccess = jest.fn();
  const mockOnScanError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders initial state with scan options', () => {
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    expect(screen.getByText('Scan Your QR Code')).toBeInTheDocument();
    expect(screen.getByText('Use Camera')).toBeInTheDocument();
    expect(screen.getByText('Upload Image')).toBeInTheDocument();
    expect(screen.getByText('Point your camera at the QR code or upload an image containing the QR code')).toBeInTheDocument();
  });

  test('shows camera scanner when Use Camera is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    const cameraButton = screen.getByText('Use Camera');
    await user.click(cameraButton);

    await waitFor(() => {
      expect(screen.getByText('Camera Scanner')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /close/i })).toBeInTheDocument();
    });

    expect(mockHtml5QrcodeScanner.render).toHaveBeenCalled();
  });

  test('handles file upload when Upload Image is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    // Mock file input
    const file = new File(['test'], 'test.png', { type: 'image/png' });
    const fileInput = screen.getByRole('button', { name: /upload image/i }).parentElement?.querySelector('input[type="file"]');
    
    expect(fileInput).toBeInTheDocument();
    
    if (fileInput) {
      await user.upload(fileInput, file);
    }

    // Should show processing state
    await waitFor(() => {
      expect(screen.getByText('Processing Image...')).toBeInTheDocument();
    });
  });

  test('calls onScanSuccess when QR code is successfully scanned', async () => {
    const user = userEvent.setup();
    const testQRData = 'https://example.com/qr/test-token';
    
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    const cameraButton = screen.getByText('Use Camera');
    await user.click(cameraButton);

    // Simulate successful scan by calling the success callback
    const renderCall = mockHtml5QrcodeScanner.render.mock.calls[0];
    const successCallback = renderCall[0];
    
    successCallback(testQRData, { text: testQRData });

    expect(mockOnScanSuccess).toHaveBeenCalledWith(testQRData);
  });

  test('calls onScanError when scanning fails', async () => {
    const user = userEvent.setup();
    const testError = 'Failed to scan QR code';
    
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    const cameraButton = screen.getByText('Use Camera');
    await user.click(cameraButton);

    // Simulate scan error by calling the error callback
    const renderCall = mockHtml5QrcodeScanner.render.mock.calls[0];
    const errorCallback = renderCall[1];
    
    errorCallback(testError);

    expect(mockOnScanError).toHaveBeenCalledWith(testError);
  });

  test('closes scanner when close button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    // Open camera scanner
    const cameraButton = screen.getByText('Use Camera');
    await user.click(cameraButton);

    await waitFor(() => {
      expect(screen.getByText('Camera Scanner')).toBeInTheDocument();
    });

    // Close scanner
    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);

    await waitFor(() => {
      expect(screen.getByText('Scan Your QR Code')).toBeInTheDocument();
      expect(screen.queryByText('Camera Scanner')).not.toBeInTheDocument();
    });

    expect(mockHtml5QrcodeScanner.clear).toHaveBeenCalled();
  });

  test('handles file scan success', async () => {
    const user = userEvent.setup();
    const testQRData = 'https://example.com/qr/test-token';
    
    mockHtml5Qrcode.scanFile.mockResolvedValueOnce(testQRData);
    
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    const file = new File(['test'], 'test.png', { type: 'image/png' });
    const fileInput = screen.getByRole('button', { name: /upload image/i }).parentElement?.querySelector('input[type="file"]');
    
    if (fileInput) {
      await user.upload(fileInput, file);
    }

    await waitFor(() => {
      expect(mockOnScanSuccess).toHaveBeenCalledWith(testQRData);
    });
  });

  test('handles file scan error', async () => {
    const user = userEvent.setup();
    const testError = new Error('Failed to scan file');
    
    mockHtml5Qrcode.scanFile.mockRejectedValueOnce(testError);
    
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    const file = new File(['test'], 'test.png', { type: 'image/png' });
    const fileInput = screen.getByRole('button', { name: /upload image/i }).parentElement?.querySelector('input[type="file"]');
    
    if (fileInput) {
      await user.upload(fileInput, file);
    }

    await waitFor(() => {
      expect(mockOnScanError).toHaveBeenCalledWith('Failed to scan QR code from image');
    });
  });

  test('applies custom width and height props', () => {
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
        width={400}
        height={400}
      />
    );

    // The component should accept these props without error
    expect(screen.getByText('Scan Your QR Code')).toBeInTheDocument();
  });

  test('cleans up scanner on unmount', () => {
    const { unmount } = render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    unmount();

    // The cleanup should not throw any errors
    expect(true).toBe(true);
  });

  test('does not call onScanError for "No QR code found" messages', async () => {
    const user = userEvent.setup();
    
    render(
      <QRScanner 
        onScanSuccess={mockOnScanSuccess}
        onScanError={mockOnScanError}
      />
    );

    const cameraButton = screen.getByText('Use Camera');
    await user.click(cameraButton);

    // Simulate "No QR code found" error (should not call onScanError)
    const renderCall = mockHtml5QrcodeScanner.render.mock.calls[0];
    const errorCallback = renderCall[1];
    
    errorCallback('No QR code found');

    expect(mockOnScanError).not.toHaveBeenCalled();
  });
});
