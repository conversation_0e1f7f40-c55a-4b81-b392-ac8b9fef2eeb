import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin, TABLES } from '@/lib/supabase';
import { isValidQRToken } from '@/utils/qr';
import { ApiResponse, User } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token || !isValidQRToken(token)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid QR token format'
      }, { status: 400 });
    }

    // Find user by QR token
    const { data: mapping, error: mappingError } = await supabaseAdmin
      .from(TABLES.QR_CODE_MAPPINGS)
      .select(`
        user_id,
        scan_count,
        users (
          id,
          name,
          email,
          zodiac_sign,
          birth_date,
          qr_token,
          language_preference,
          created_at,
          updated_at
        )
      `)
      .eq('qr_token', token)
      .single();

    if (mappingError || !mapping) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'QR code not found or invalid'
      }, { status: 404 });
    }

    // Update scan count and last scanned time
    await supabaseAdmin
      .from(TABLES.QR_CODE_MAPPINGS)
      .update({
        scan_count: mapping.scan_count + 1,
        last_scanned: new Date().toISOString()
      })
      .eq('qr_token', token);

    const user = mapping.users as User;

    return NextResponse.json<ApiResponse<User>>({
      success: true,
      data: user,
      message: 'Authentication successful'
    });

  } catch (error) {
    console.error('QR authentication error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token = searchParams.get('token');

  if (!token || !isValidQRToken(token)) {
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Invalid or missing QR token'
    }, { status: 400 });
  }

  try {
    // Check if QR token exists
    const { data: mapping, error } = await supabaseAdmin
      .from(TABLES.QR_CODE_MAPPINGS)
      .select('user_id')
      .eq('qr_token', token)
      .single();

    if (error || !mapping) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'QR code not found'
      }, { status: 404 });
    }

    return NextResponse.json<ApiResponse<{ exists: boolean }>>({
      success: true,
      data: { exists: true },
      message: 'QR token is valid'
    });

  } catch (error) {
    console.error('QR validation error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
