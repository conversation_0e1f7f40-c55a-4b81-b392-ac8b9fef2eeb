import { ZodiacSign } from '@/types';

export const ZODIAC_SIGNS: ZodiacSign[] = [
  'aries', 'taurus', 'gemini', 'cancer',
  'leo', 'virgo', 'libra', 'scorpio',
  'sagittarius', 'capricorn', 'aquarius', 'pisces'
];

export const ZODIAC_INFO = {
  aries: { name: '<PERSON><PERSON>', symbol: '♈', dates: 'Mar 21 - Apr 19', element: 'Fire' },
  taurus: { name: 'Taurus', symbol: '♉', dates: 'Apr 20 - May 20', element: 'Earth' },
  gemini: { name: 'Gemini', symbol: '♊', dates: 'May 21 - Jun 20', element: 'Air' },
  cancer: { name: 'Cancer', symbol: '♋', dates: 'Jun 21 - Jul 22', element: 'Water' },
  leo: { name: '<PERSON>', symbol: '♌', dates: 'Jul 23 - Aug 22', element: 'Fire' },
  virgo: { name: 'Virgo', symbol: '♍', dates: 'Aug 23 - Sep 22', element: 'Earth' },
  libra: { name: '<PERSON><PERSON>', symbol: '♎', dates: 'Sep 23 - Oct 22', element: 'Air' },
  scorpio: { name: '<PERSON><PERSON><PERSON>', symbol: '♏', dates: 'Oct 23 - Nov 21', element: 'Water' },
  sagittarius: { name: 'Sagittarius', symbol: '♐', dates: 'Nov 22 - Dec 21', element: 'Fire' },
  capricorn: { name: 'Capricorn', symbol: '♑', dates: 'Dec 22 - Jan 19', element: 'Earth' },
  aquarius: { name: 'Aquarius', symbol: '♒', dates: 'Jan 20 - Feb 18', element: 'Air' },
  pisces: { name: 'Pisces', symbol: '♓', dates: 'Feb 19 - Mar 20', element: 'Water' }
};

export function getZodiacFromDate(birthDate: string): ZodiacSign {
  const date = new Date(birthDate);
  const month = date.getMonth() + 1; // 1-12
  const day = date.getDate();

  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'aries';
  if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'taurus';
  if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'gemini';
  if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'cancer';
  if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'leo';
  if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'virgo';
  if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'libra';
  if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'scorpio';
  if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'sagittarius';
  if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'capricorn';
  if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'aquarius';
  return 'pisces';
}

export function getZodiacColors(sign: ZodiacSign): string[] {
  const colorMap = {
    aries: ['#FF6B6B', '#FF4757'],
    taurus: ['#2ECC71', '#27AE60'],
    gemini: ['#F39C12', '#E67E22'],
    cancer: ['#3498DB', '#2980B9'],
    leo: ['#E74C3C', '#C0392B'],
    virgo: ['#1ABC9C', '#16A085'],
    libra: ['#9B59B6', '#8E44AD'],
    scorpio: ['#34495E', '#2C3E50'],
    sagittarius: ['#E67E22', '#D35400'],
    capricorn: ['#95A5A6', '#7F8C8D'],
    aquarius: ['#3498DB', '#2980B9'],
    pisces: ['#1ABC9C', '#16A085']
  };
  return colorMap[sign];
}
