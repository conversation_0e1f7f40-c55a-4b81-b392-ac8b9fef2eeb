import { NextResponse } from 'next/server';
import { prisma, checkDatabaseConnection } from '@/lib/prisma';

export async function GET() {
  try {
    // Check database connectivity
    const isDatabaseConnected = await checkDatabaseConnection();

    if (!isDatabaseConnected) {
      console.error('Database health check failed');
      return NextResponse.json(
        {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          checks: {
            database: 'failed',
            api: 'ok'
          },
          error: 'Database connection failed'
        },
        { status: 503 }
      );
    }

    // Check environment variables
    const requiredEnvVars = [
      'DATABASE_URL'
    ];

    const missingEnvVars = requiredEnvVars.filter(
      envVar => !process.env[envVar]
    );

    if (missingEnvVars.length > 0) {
      return NextResponse.json(
        {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          checks: {
            database: 'ok',
            api: 'ok',
            environment: 'failed'
          },
          error: `Missing environment variables: ${missingEnvVars.join(', ')}`
        },
        { status: 503 }
      );
    }

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: 'ok',
        api: 'ok',
        environment: 'ok'
      },
      uptime: process.uptime()
    });

  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'unknown',
          api: 'failed'
        },
        error: 'Internal server error'
      },
      { status: 500 }
    );
  }
}
