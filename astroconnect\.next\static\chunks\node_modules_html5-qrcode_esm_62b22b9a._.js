(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/html5-qrcode/esm/core.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseLoggger": (()=>BaseLoggger),
    "DecodedTextType": (()=>DecodedTextType),
    "Html5QrcodeConstants": (()=>Html5QrcodeConstants),
    "Html5QrcodeErrorFactory": (()=>Html5QrcodeErrorFactory),
    "Html5QrcodeErrorTypes": (()=>Html5QrcodeErrorTypes),
    "Html5QrcodeResultFactory": (()=>Html5QrcodeResultFactory),
    "Html5QrcodeScanType": (()=>Html5QrcodeScanType),
    "Html5QrcodeSupportedFormats": (()=>Html5QrcodeSupportedFormats),
    "QrcodeResultFormat": (()=>QrcodeResultFormat),
    "clip": (()=>clip),
    "isNullOrUndefined": (()=>isNullOrUndefined),
    "isValidHtml5QrcodeSupportedFormats": (()=>isValidHtml5QrcodeSupportedFormats)
});
var Html5QrcodeSupportedFormats;
(function(Html5QrcodeSupportedFormats) {
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["QR_CODE"] = 0] = "QR_CODE";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["AZTEC"] = 1] = "AZTEC";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["CODABAR"] = 2] = "CODABAR";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["CODE_39"] = 3] = "CODE_39";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["CODE_93"] = 4] = "CODE_93";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["CODE_128"] = 5] = "CODE_128";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["DATA_MATRIX"] = 6] = "DATA_MATRIX";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["MAXICODE"] = 7] = "MAXICODE";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["ITF"] = 8] = "ITF";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["EAN_13"] = 9] = "EAN_13";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["EAN_8"] = 10] = "EAN_8";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["PDF_417"] = 11] = "PDF_417";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["RSS_14"] = 12] = "RSS_14";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["RSS_EXPANDED"] = 13] = "RSS_EXPANDED";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["UPC_A"] = 14] = "UPC_A";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["UPC_E"] = 15] = "UPC_E";
    Html5QrcodeSupportedFormats[Html5QrcodeSupportedFormats["UPC_EAN_EXTENSION"] = 16] = "UPC_EAN_EXTENSION";
})(Html5QrcodeSupportedFormats || (Html5QrcodeSupportedFormats = {}));
var html5QrcodeSupportedFormatsTextMap = new Map([
    [
        Html5QrcodeSupportedFormats.QR_CODE,
        "QR_CODE"
    ],
    [
        Html5QrcodeSupportedFormats.AZTEC,
        "AZTEC"
    ],
    [
        Html5QrcodeSupportedFormats.CODABAR,
        "CODABAR"
    ],
    [
        Html5QrcodeSupportedFormats.CODE_39,
        "CODE_39"
    ],
    [
        Html5QrcodeSupportedFormats.CODE_93,
        "CODE_93"
    ],
    [
        Html5QrcodeSupportedFormats.CODE_128,
        "CODE_128"
    ],
    [
        Html5QrcodeSupportedFormats.DATA_MATRIX,
        "DATA_MATRIX"
    ],
    [
        Html5QrcodeSupportedFormats.MAXICODE,
        "MAXICODE"
    ],
    [
        Html5QrcodeSupportedFormats.ITF,
        "ITF"
    ],
    [
        Html5QrcodeSupportedFormats.EAN_13,
        "EAN_13"
    ],
    [
        Html5QrcodeSupportedFormats.EAN_8,
        "EAN_8"
    ],
    [
        Html5QrcodeSupportedFormats.PDF_417,
        "PDF_417"
    ],
    [
        Html5QrcodeSupportedFormats.RSS_14,
        "RSS_14"
    ],
    [
        Html5QrcodeSupportedFormats.RSS_EXPANDED,
        "RSS_EXPANDED"
    ],
    [
        Html5QrcodeSupportedFormats.UPC_A,
        "UPC_A"
    ],
    [
        Html5QrcodeSupportedFormats.UPC_E,
        "UPC_E"
    ],
    [
        Html5QrcodeSupportedFormats.UPC_EAN_EXTENSION,
        "UPC_EAN_EXTENSION"
    ]
]);
var DecodedTextType;
(function(DecodedTextType) {
    DecodedTextType[DecodedTextType["UNKNOWN"] = 0] = "UNKNOWN";
    DecodedTextType[DecodedTextType["URL"] = 1] = "URL";
})(DecodedTextType || (DecodedTextType = {}));
function isValidHtml5QrcodeSupportedFormats(format) {
    return Object.values(Html5QrcodeSupportedFormats).includes(format);
}
var Html5QrcodeScanType;
(function(Html5QrcodeScanType) {
    Html5QrcodeScanType[Html5QrcodeScanType["SCAN_TYPE_CAMERA"] = 0] = "SCAN_TYPE_CAMERA";
    Html5QrcodeScanType[Html5QrcodeScanType["SCAN_TYPE_FILE"] = 1] = "SCAN_TYPE_FILE";
})(Html5QrcodeScanType || (Html5QrcodeScanType = {}));
var Html5QrcodeConstants = function() {
    function Html5QrcodeConstants() {}
    Html5QrcodeConstants.GITHUB_PROJECT_URL = "https://github.com/mebjas/html5-qrcode";
    Html5QrcodeConstants.SCAN_DEFAULT_FPS = 2;
    Html5QrcodeConstants.DEFAULT_DISABLE_FLIP = false;
    Html5QrcodeConstants.DEFAULT_REMEMBER_LAST_CAMERA_USED = true;
    Html5QrcodeConstants.DEFAULT_SUPPORTED_SCAN_TYPE = [
        Html5QrcodeScanType.SCAN_TYPE_CAMERA,
        Html5QrcodeScanType.SCAN_TYPE_FILE
    ];
    return Html5QrcodeConstants;
}();
;
var QrcodeResultFormat = function() {
    function QrcodeResultFormat(format, formatName) {
        this.format = format;
        this.formatName = formatName;
    }
    QrcodeResultFormat.prototype.toString = function() {
        return this.formatName;
    };
    QrcodeResultFormat.create = function(format) {
        if (!html5QrcodeSupportedFormatsTextMap.has(format)) {
            throw "".concat(format, " not in html5QrcodeSupportedFormatsTextMap");
        }
        return new QrcodeResultFormat(format, html5QrcodeSupportedFormatsTextMap.get(format));
    };
    return QrcodeResultFormat;
}();
;
var Html5QrcodeResultFactory = function() {
    function Html5QrcodeResultFactory() {}
    Html5QrcodeResultFactory.createFromText = function(decodedText) {
        var qrcodeResult = {
            text: decodedText
        };
        return {
            decodedText: decodedText,
            result: qrcodeResult
        };
    };
    Html5QrcodeResultFactory.createFromQrcodeResult = function(qrcodeResult) {
        return {
            decodedText: qrcodeResult.text,
            result: qrcodeResult
        };
    };
    return Html5QrcodeResultFactory;
}();
;
var Html5QrcodeErrorTypes;
(function(Html5QrcodeErrorTypes) {
    Html5QrcodeErrorTypes[Html5QrcodeErrorTypes["UNKWOWN_ERROR"] = 0] = "UNKWOWN_ERROR";
    Html5QrcodeErrorTypes[Html5QrcodeErrorTypes["IMPLEMENTATION_ERROR"] = 1] = "IMPLEMENTATION_ERROR";
    Html5QrcodeErrorTypes[Html5QrcodeErrorTypes["NO_CODE_FOUND_ERROR"] = 2] = "NO_CODE_FOUND_ERROR";
})(Html5QrcodeErrorTypes || (Html5QrcodeErrorTypes = {}));
var Html5QrcodeErrorFactory = function() {
    function Html5QrcodeErrorFactory() {}
    Html5QrcodeErrorFactory.createFrom = function(error) {
        return {
            errorMessage: error,
            type: Html5QrcodeErrorTypes.UNKWOWN_ERROR
        };
    };
    return Html5QrcodeErrorFactory;
}();
;
var BaseLoggger = function() {
    function BaseLoggger(verbose) {
        this.verbose = verbose;
    }
    BaseLoggger.prototype.log = function(message) {
        if (this.verbose) {
            console.log(message);
        }
    };
    BaseLoggger.prototype.warn = function(message) {
        if (this.verbose) {
            console.warn(message);
        }
    };
    BaseLoggger.prototype.logError = function(message, isExperimental) {
        if (this.verbose || isExperimental === true) {
            console.error(message);
        }
    };
    BaseLoggger.prototype.logErrors = function(errors) {
        if (errors.length === 0) {
            throw "Logger#logError called without arguments";
        }
        if (this.verbose) {
            console.error(errors);
        }
    };
    return BaseLoggger;
}();
;
function isNullOrUndefined(obj) {
    return typeof obj === "undefined" || obj === null;
}
function clip(value, minValue, maxValue) {
    if (value > maxValue) {
        return maxValue;
    }
    if (value < minValue) {
        return minValue;
    }
    return value;
} //# sourceMappingURL=core.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html5QrcodeScannerStrings": (()=>Html5QrcodeScannerStrings),
    "Html5QrcodeStrings": (()=>Html5QrcodeStrings),
    "LibraryInfoStrings": (()=>LibraryInfoStrings)
});
var Html5QrcodeStrings = function() {
    function Html5QrcodeStrings() {}
    Html5QrcodeStrings.codeParseError = function(exception) {
        return "QR code parse error, error = ".concat(exception);
    };
    Html5QrcodeStrings.errorGettingUserMedia = function(error) {
        return "Error getting userMedia, error = ".concat(error);
    };
    Html5QrcodeStrings.onlyDeviceSupportedError = function() {
        return "The device doesn't support navigator.mediaDevices , only " + "supported cameraIdOrConfig in this case is deviceId parameter " + "(string).";
    };
    Html5QrcodeStrings.cameraStreamingNotSupported = function() {
        return "Camera streaming not supported by the browser.";
    };
    Html5QrcodeStrings.unableToQuerySupportedDevices = function() {
        return "Unable to query supported devices, unknown error.";
    };
    Html5QrcodeStrings.insecureContextCameraQueryError = function() {
        return "Camera access is only supported in secure context like https " + "or localhost.";
    };
    Html5QrcodeStrings.scannerPaused = function() {
        return "Scanner paused";
    };
    return Html5QrcodeStrings;
}();
;
var Html5QrcodeScannerStrings = function() {
    function Html5QrcodeScannerStrings() {}
    Html5QrcodeScannerStrings.scanningStatus = function() {
        return "Scanning";
    };
    Html5QrcodeScannerStrings.idleStatus = function() {
        return "Idle";
    };
    Html5QrcodeScannerStrings.errorStatus = function() {
        return "Error";
    };
    Html5QrcodeScannerStrings.permissionStatus = function() {
        return "Permission";
    };
    Html5QrcodeScannerStrings.noCameraFoundErrorStatus = function() {
        return "No Cameras";
    };
    Html5QrcodeScannerStrings.lastMatch = function(decodedText) {
        return "Last Match: ".concat(decodedText);
    };
    Html5QrcodeScannerStrings.codeScannerTitle = function() {
        return "Code Scanner";
    };
    Html5QrcodeScannerStrings.cameraPermissionTitle = function() {
        return "Request Camera Permissions";
    };
    Html5QrcodeScannerStrings.cameraPermissionRequesting = function() {
        return "Requesting camera permissions...";
    };
    Html5QrcodeScannerStrings.noCameraFound = function() {
        return "No camera found";
    };
    Html5QrcodeScannerStrings.scanButtonStopScanningText = function() {
        return "Stop Scanning";
    };
    Html5QrcodeScannerStrings.scanButtonStartScanningText = function() {
        return "Start Scanning";
    };
    Html5QrcodeScannerStrings.torchOnButton = function() {
        return "Switch On Torch";
    };
    Html5QrcodeScannerStrings.torchOffButton = function() {
        return "Switch Off Torch";
    };
    Html5QrcodeScannerStrings.torchOnFailedMessage = function() {
        return "Failed to turn on torch";
    };
    Html5QrcodeScannerStrings.torchOffFailedMessage = function() {
        return "Failed to turn off torch";
    };
    Html5QrcodeScannerStrings.scanButtonScanningStarting = function() {
        return "Launching Camera...";
    };
    Html5QrcodeScannerStrings.textIfCameraScanSelected = function() {
        return "Scan an Image File";
    };
    Html5QrcodeScannerStrings.textIfFileScanSelected = function() {
        return "Scan using camera directly";
    };
    Html5QrcodeScannerStrings.selectCamera = function() {
        return "Select Camera";
    };
    Html5QrcodeScannerStrings.fileSelectionChooseImage = function() {
        return "Choose Image";
    };
    Html5QrcodeScannerStrings.fileSelectionChooseAnother = function() {
        return "Choose Another";
    };
    Html5QrcodeScannerStrings.fileSelectionNoImageSelected = function() {
        return "No image choosen";
    };
    Html5QrcodeScannerStrings.anonymousCameraPrefix = function() {
        return "Anonymous Camera";
    };
    Html5QrcodeScannerStrings.dragAndDropMessage = function() {
        return "Or drop an image to scan";
    };
    Html5QrcodeScannerStrings.dragAndDropMessageOnlyImages = function() {
        return "Or drop an image to scan (other files not supported)";
    };
    Html5QrcodeScannerStrings.zoom = function() {
        return "zoom";
    };
    Html5QrcodeScannerStrings.loadingImage = function() {
        return "Loading image...";
    };
    Html5QrcodeScannerStrings.cameraScanAltText = function() {
        return "Camera based scan";
    };
    Html5QrcodeScannerStrings.fileScanAltText = function() {
        return "Fule based scan";
    };
    return Html5QrcodeScannerStrings;
}();
;
var LibraryInfoStrings = function() {
    function LibraryInfoStrings() {}
    LibraryInfoStrings.poweredBy = function() {
        return "Powered by ";
    };
    LibraryInfoStrings.reportIssues = function() {
        return "Report issues";
    };
    return LibraryInfoStrings;
}();
;
 //# sourceMappingURL=strings.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "VideoConstraintsUtil": (()=>VideoConstraintsUtil)
});
var VideoConstraintsUtil = function() {
    function VideoConstraintsUtil() {}
    VideoConstraintsUtil.isMediaStreamConstraintsValid = function(videoConstraints, logger) {
        if (typeof videoConstraints !== "object") {
            var typeofVideoConstraints = typeof videoConstraints;
            logger.logError("videoConstraints should be of type object, the " + "object passed is of type ".concat(typeofVideoConstraints, "."), true);
            return false;
        }
        var bannedKeys = [
            "autoGainControl",
            "channelCount",
            "echoCancellation",
            "latency",
            "noiseSuppression",
            "sampleRate",
            "sampleSize",
            "volume"
        ];
        var bannedkeysSet = new Set(bannedKeys);
        var keysInVideoConstraints = Object.keys(videoConstraints);
        for(var _i = 0, keysInVideoConstraints_1 = keysInVideoConstraints; _i < keysInVideoConstraints_1.length; _i++){
            var key = keysInVideoConstraints_1[_i];
            if (bannedkeysSet.has(key)) {
                logger.logError("".concat(key, " is not supported videoConstaints."), true);
                return false;
            }
        }
        return true;
    };
    return VideoConstraintsUtil;
}();
;
 //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/zxing-html5-qrcode-decoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ZXingHtml5QrcodeDecoder": (()=>ZXingHtml5QrcodeDecoder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/third_party/zxing-js.umd.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/core.js [app-client] (ecmascript)");
;
;
var ZXingHtml5QrcodeDecoder = function() {
    function ZXingHtml5QrcodeDecoder(requestedFormats, verbose, logger) {
        this.formatMap = new Map([
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].QR_CODE,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.QR_CODE
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].AZTEC,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.AZTEC
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODABAR,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.CODABAR
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_39,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.CODE_39
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_93,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.CODE_93
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_128,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.CODE_128
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].DATA_MATRIX,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.DATA_MATRIX
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].MAXICODE,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.MAXICODE
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].ITF,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.ITF
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].EAN_13,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.EAN_13
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].EAN_8,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.EAN_8
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].PDF_417,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.PDF_417
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].RSS_14,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.RSS_14
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].RSS_EXPANDED,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.RSS_EXPANDED
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].UPC_A,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.UPC_A
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].UPC_E,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.UPC_E
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].UPC_EAN_EXTENSION,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BarcodeFormat.UPC_EAN_EXTENSION
            ]
        ]);
        this.reverseFormatMap = this.createReverseFormatMap();
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__) {
            throw "Use html5qrcode.min.js without edit, ZXing not found.";
        }
        this.verbose = verbose;
        this.logger = logger;
        var formats = this.createZXingFormats(requestedFormats);
        var hints = new Map();
        hints.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.DecodeHintType.POSSIBLE_FORMATS, formats);
        hints.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.DecodeHintType.TRY_HARDER, false);
        this.hints = hints;
    }
    ZXingHtml5QrcodeDecoder.prototype.decodeAsync = function(canvas) {
        var _this = this;
        return new Promise(function(resolve, reject) {
            try {
                resolve(_this.decode(canvas));
            } catch (error) {
                reject(error);
            }
        });
    };
    ZXingHtml5QrcodeDecoder.prototype.decode = function(canvas) {
        var zxingDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.MultiFormatReader(this.verbose, this.hints);
        var luminanceSource = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.HTMLCanvasElementLuminanceSource(canvas);
        var binaryBitmap = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.BinaryBitmap(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$third_party$2f$zxing$2d$js$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.HybridBinarizer(luminanceSource));
        var result = zxingDecoder.decode(binaryBitmap);
        return {
            text: result.text,
            format: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QrcodeResultFormat"].create(this.toHtml5QrcodeSupportedFormats(result.format)),
            debugData: this.createDebugData()
        };
    };
    ZXingHtml5QrcodeDecoder.prototype.createReverseFormatMap = function() {
        var result = new Map();
        this.formatMap.forEach(function(value, key, _) {
            result.set(value, key);
        });
        return result;
    };
    ZXingHtml5QrcodeDecoder.prototype.toHtml5QrcodeSupportedFormats = function(zxingFormat) {
        if (!this.reverseFormatMap.has(zxingFormat)) {
            throw "reverseFormatMap doesn't have ".concat(zxingFormat);
        }
        return this.reverseFormatMap.get(zxingFormat);
    };
    ZXingHtml5QrcodeDecoder.prototype.createZXingFormats = function(requestedFormats) {
        var zxingFormats = [];
        for(var _i = 0, requestedFormats_1 = requestedFormats; _i < requestedFormats_1.length; _i++){
            var requestedFormat = requestedFormats_1[_i];
            if (this.formatMap.has(requestedFormat)) {
                zxingFormats.push(this.formatMap.get(requestedFormat));
            } else {
                this.logger.logError("".concat(requestedFormat, " is not supported by") + "ZXingHtml5QrcodeShim");
            }
        }
        return zxingFormats;
    };
    ZXingHtml5QrcodeDecoder.prototype.createDebugData = function() {
        return {
            decoderName: "zxing-js"
        };
    };
    return ZXingHtml5QrcodeDecoder;
}();
;
 //# sourceMappingURL=zxing-html5-qrcode-decoder.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/native-bar-code-detector.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BarcodeDetectorDelegate": (()=>BarcodeDetectorDelegate)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/core.js [app-client] (ecmascript)");
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
;
var BarcodeDetectorDelegate = function() {
    function BarcodeDetectorDelegate(requestedFormats, verbose, logger) {
        this.formatMap = new Map([
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].QR_CODE,
                "qr_code"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].AZTEC,
                "aztec"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODABAR,
                "codabar"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_39,
                "code_39"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_93,
                "code_93"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_128,
                "code_128"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].DATA_MATRIX,
                "data_matrix"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].ITF,
                "itf"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].EAN_13,
                "ean_13"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].EAN_8,
                "ean_8"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].PDF_417,
                "pdf417"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].UPC_A,
                "upc_a"
            ],
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].UPC_E,
                "upc_e"
            ]
        ]);
        this.reverseFormatMap = this.createReverseFormatMap();
        if (!BarcodeDetectorDelegate.isSupported()) {
            throw "Use html5qrcode.min.js without edit, Use " + "BarcodeDetectorDelegate only if it isSupported();";
        }
        this.verbose = verbose;
        this.logger = logger;
        var formats = this.createBarcodeDetectorFormats(requestedFormats);
        this.detector = new BarcodeDetector(formats);
        if (!this.detector) {
            throw "BarcodeDetector detector not supported";
        }
    }
    BarcodeDetectorDelegate.isSupported = function() {
        if (!("BarcodeDetector" in window)) {
            return false;
        }
        var dummyDetector = new BarcodeDetector({
            formats: [
                "qr_code"
            ]
        });
        return typeof dummyDetector !== "undefined";
    };
    BarcodeDetectorDelegate.prototype.decodeAsync = function(canvas) {
        return __awaiter(this, void 0, void 0, function() {
            var barcodes, largestBarcode;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4,
                            this.detector.detect(canvas)
                        ];
                    case 1:
                        barcodes = _a.sent();
                        if (!barcodes || barcodes.length === 0) {
                            throw "No barcode or QR code detected.";
                        }
                        largestBarcode = this.selectLargestBarcode(barcodes);
                        return [
                            2,
                            {
                                text: largestBarcode.rawValue,
                                format: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QrcodeResultFormat"].create(this.toHtml5QrcodeSupportedFormats(largestBarcode.format)),
                                debugData: this.createDebugData()
                            }
                        ];
                }
            });
        });
    };
    BarcodeDetectorDelegate.prototype.selectLargestBarcode = function(barcodes) {
        var largestBarcode = null;
        var maxArea = 0;
        for(var _i = 0, barcodes_1 = barcodes; _i < barcodes_1.length; _i++){
            var barcode = barcodes_1[_i];
            var area = barcode.boundingBox.width * barcode.boundingBox.height;
            if (area > maxArea) {
                maxArea = area;
                largestBarcode = barcode;
            }
        }
        if (!largestBarcode) {
            throw "No largest barcode found";
        }
        return largestBarcode;
    };
    BarcodeDetectorDelegate.prototype.createBarcodeDetectorFormats = function(requestedFormats) {
        var formats = [];
        for(var _i = 0, requestedFormats_1 = requestedFormats; _i < requestedFormats_1.length; _i++){
            var requestedFormat = requestedFormats_1[_i];
            if (this.formatMap.has(requestedFormat)) {
                formats.push(this.formatMap.get(requestedFormat));
            } else {
                this.logger.warn("".concat(requestedFormat, " is not supported by") + "BarcodeDetectorDelegate");
            }
        }
        return {
            formats: formats
        };
    };
    BarcodeDetectorDelegate.prototype.toHtml5QrcodeSupportedFormats = function(barcodeDetectorFormat) {
        if (!this.reverseFormatMap.has(barcodeDetectorFormat)) {
            throw "reverseFormatMap doesn't have ".concat(barcodeDetectorFormat);
        }
        return this.reverseFormatMap.get(barcodeDetectorFormat);
    };
    BarcodeDetectorDelegate.prototype.createReverseFormatMap = function() {
        var result = new Map();
        this.formatMap.forEach(function(value, key, _) {
            result.set(value, key);
        });
        return result;
    };
    BarcodeDetectorDelegate.prototype.createDebugData = function() {
        return {
            decoderName: "BarcodeDetector"
        };
    };
    return BarcodeDetectorDelegate;
}();
;
 //# sourceMappingURL=native-bar-code-detector.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/code-decoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html5QrcodeShim": (()=>Html5QrcodeShim)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$zxing$2d$html5$2d$qrcode$2d$decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/zxing-html5-qrcode-decoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$native$2d$bar$2d$code$2d$detector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/native-bar-code-detector.js [app-client] (ecmascript)");
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
;
;
var Html5QrcodeShim = function() {
    function Html5QrcodeShim(requestedFormats, useBarCodeDetectorIfSupported, verbose, logger) {
        this.EXECUTIONS_TO_REPORT_PERFORMANCE = 100;
        this.executions = 0;
        this.executionResults = [];
        this.wasPrimaryDecoderUsedInLastDecode = false;
        this.verbose = verbose;
        if (useBarCodeDetectorIfSupported && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$native$2d$bar$2d$code$2d$detector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BarcodeDetectorDelegate"].isSupported()) {
            this.primaryDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$native$2d$bar$2d$code$2d$detector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BarcodeDetectorDelegate"](requestedFormats, verbose, logger);
            this.secondaryDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$zxing$2d$html5$2d$qrcode$2d$decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ZXingHtml5QrcodeDecoder"](requestedFormats, verbose, logger);
        } else {
            this.primaryDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$zxing$2d$html5$2d$qrcode$2d$decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ZXingHtml5QrcodeDecoder"](requestedFormats, verbose, logger);
        }
    }
    Html5QrcodeShim.prototype.decodeAsync = function(canvas) {
        return __awaiter(this, void 0, void 0, function() {
            var startTime;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        startTime = performance.now();
                        _a.label = 1;
                    case 1:
                        _a.trys.push([
                            1,
                            ,
                            3,
                            4
                        ]);
                        return [
                            4,
                            this.getDecoder().decodeAsync(canvas)
                        ];
                    case 2:
                        return [
                            2,
                            _a.sent()
                        ];
                    case 3:
                        this.possiblyLogPerformance(startTime);
                        return [
                            7
                        ];
                    case 4:
                        return [
                            2
                        ];
                }
            });
        });
    };
    Html5QrcodeShim.prototype.decodeRobustlyAsync = function(canvas) {
        return __awaiter(this, void 0, void 0, function() {
            var startTime, error_1;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        startTime = performance.now();
                        _a.label = 1;
                    case 1:
                        _a.trys.push([
                            1,
                            3,
                            4,
                            5
                        ]);
                        return [
                            4,
                            this.primaryDecoder.decodeAsync(canvas)
                        ];
                    case 2:
                        return [
                            2,
                            _a.sent()
                        ];
                    case 3:
                        error_1 = _a.sent();
                        if (this.secondaryDecoder) {
                            return [
                                2,
                                this.secondaryDecoder.decodeAsync(canvas)
                            ];
                        }
                        throw error_1;
                    case 4:
                        this.possiblyLogPerformance(startTime);
                        return [
                            7
                        ];
                    case 5:
                        return [
                            2
                        ];
                }
            });
        });
    };
    Html5QrcodeShim.prototype.getDecoder = function() {
        if (!this.secondaryDecoder) {
            return this.primaryDecoder;
        }
        if (this.wasPrimaryDecoderUsedInLastDecode === false) {
            this.wasPrimaryDecoderUsedInLastDecode = true;
            return this.primaryDecoder;
        }
        this.wasPrimaryDecoderUsedInLastDecode = false;
        return this.secondaryDecoder;
    };
    Html5QrcodeShim.prototype.possiblyLogPerformance = function(startTime) {
        if (!this.verbose) {
            return;
        }
        var executionTime = performance.now() - startTime;
        this.executionResults.push(executionTime);
        this.executions++;
        this.possiblyFlushPerformanceReport();
    };
    Html5QrcodeShim.prototype.possiblyFlushPerformanceReport = function() {
        if (this.executions < this.EXECUTIONS_TO_REPORT_PERFORMANCE) {
            return;
        }
        var sum = 0;
        for(var _i = 0, _a = this.executionResults; _i < _a.length; _i++){
            var executionTime = _a[_i];
            sum += executionTime;
        }
        var mean = sum / this.executionResults.length;
        console.log("".concat(mean, " ms for ").concat(this.executionResults.length, " last runs."));
        this.executions = 0;
        this.executionResults = [];
    };
    return Html5QrcodeShim;
}();
;
 //# sourceMappingURL=code-decoder.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/camera/core-impl.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CameraImpl": (()=>CameraImpl)
});
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
var AbstractCameraCapability = function() {
    function AbstractCameraCapability(name, track) {
        this.name = name;
        this.track = track;
    }
    AbstractCameraCapability.prototype.isSupported = function() {
        if (!this.track.getCapabilities) {
            return false;
        }
        return this.name in this.track.getCapabilities();
    };
    AbstractCameraCapability.prototype.apply = function(value) {
        var constraint = {};
        constraint[this.name] = value;
        var constraints = {
            advanced: [
                constraint
            ]
        };
        return this.track.applyConstraints(constraints);
    };
    AbstractCameraCapability.prototype.value = function() {
        var settings = this.track.getSettings();
        if (this.name in settings) {
            var settingValue = settings[this.name];
            return settingValue;
        }
        return null;
    };
    return AbstractCameraCapability;
}();
var AbstractRangeCameraCapability = function(_super) {
    __extends(AbstractRangeCameraCapability, _super);
    function AbstractRangeCameraCapability(name, track) {
        return _super.call(this, name, track) || this;
    }
    AbstractRangeCameraCapability.prototype.min = function() {
        return this.getCapabilities().min;
    };
    AbstractRangeCameraCapability.prototype.max = function() {
        return this.getCapabilities().max;
    };
    AbstractRangeCameraCapability.prototype.step = function() {
        return this.getCapabilities().step;
    };
    AbstractRangeCameraCapability.prototype.apply = function(value) {
        var constraint = {};
        constraint[this.name] = value;
        var constraints = {
            advanced: [
                constraint
            ]
        };
        return this.track.applyConstraints(constraints);
    };
    AbstractRangeCameraCapability.prototype.getCapabilities = function() {
        this.failIfNotSupported();
        var capabilities = this.track.getCapabilities();
        var capability = capabilities[this.name];
        return {
            min: capability.min,
            max: capability.max,
            step: capability.step
        };
    };
    AbstractRangeCameraCapability.prototype.failIfNotSupported = function() {
        if (!this.isSupported()) {
            throw new Error("".concat(this.name, " capability not supported"));
        }
    };
    return AbstractRangeCameraCapability;
}(AbstractCameraCapability);
var ZoomFeatureImpl = function(_super) {
    __extends(ZoomFeatureImpl, _super);
    function ZoomFeatureImpl(track) {
        return _super.call(this, "zoom", track) || this;
    }
    return ZoomFeatureImpl;
}(AbstractRangeCameraCapability);
var TorchFeatureImpl = function(_super) {
    __extends(TorchFeatureImpl, _super);
    function TorchFeatureImpl(track) {
        return _super.call(this, "torch", track) || this;
    }
    return TorchFeatureImpl;
}(AbstractCameraCapability);
var CameraCapabilitiesImpl = function() {
    function CameraCapabilitiesImpl(track) {
        this.track = track;
    }
    CameraCapabilitiesImpl.prototype.zoomFeature = function() {
        return new ZoomFeatureImpl(this.track);
    };
    CameraCapabilitiesImpl.prototype.torchFeature = function() {
        return new TorchFeatureImpl(this.track);
    };
    return CameraCapabilitiesImpl;
}();
var RenderedCameraImpl = function() {
    function RenderedCameraImpl(parentElement, mediaStream, callbacks) {
        this.isClosed = false;
        this.parentElement = parentElement;
        this.mediaStream = mediaStream;
        this.callbacks = callbacks;
        this.surface = this.createVideoElement(this.parentElement.clientWidth);
        parentElement.append(this.surface);
    }
    RenderedCameraImpl.prototype.createVideoElement = function(width) {
        var videoElement = document.createElement("video");
        videoElement.style.width = "".concat(width, "px");
        videoElement.style.display = "block";
        videoElement.muted = true;
        videoElement.setAttribute("muted", "true");
        videoElement.playsInline = true;
        return videoElement;
    };
    RenderedCameraImpl.prototype.setupSurface = function() {
        var _this = this;
        this.surface.onabort = function() {
            throw "RenderedCameraImpl video surface onabort() called";
        };
        this.surface.onerror = function() {
            throw "RenderedCameraImpl video surface onerror() called";
        };
        var onVideoStart = function() {
            var videoWidth = _this.surface.clientWidth;
            var videoHeight = _this.surface.clientHeight;
            _this.callbacks.onRenderSurfaceReady(videoWidth, videoHeight);
            _this.surface.removeEventListener("playing", onVideoStart);
        };
        this.surface.addEventListener("playing", onVideoStart);
        this.surface.srcObject = this.mediaStream;
        this.surface.play();
    };
    RenderedCameraImpl.create = function(parentElement, mediaStream, options, callbacks) {
        return __awaiter(this, void 0, void 0, function() {
            var renderedCamera, aspectRatioConstraint;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        renderedCamera = new RenderedCameraImpl(parentElement, mediaStream, callbacks);
                        if (!options.aspectRatio) return [
                            3,
                            2
                        ];
                        aspectRatioConstraint = {
                            aspectRatio: options.aspectRatio
                        };
                        return [
                            4,
                            renderedCamera.getFirstTrackOrFail().applyConstraints(aspectRatioConstraint)
                        ];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        renderedCamera.setupSurface();
                        return [
                            2,
                            renderedCamera
                        ];
                }
            });
        });
    };
    RenderedCameraImpl.prototype.failIfClosed = function() {
        if (this.isClosed) {
            throw "The RenderedCamera has already been closed.";
        }
    };
    RenderedCameraImpl.prototype.getFirstTrackOrFail = function() {
        this.failIfClosed();
        if (this.mediaStream.getVideoTracks().length === 0) {
            throw "No video tracks found";
        }
        return this.mediaStream.getVideoTracks()[0];
    };
    RenderedCameraImpl.prototype.pause = function() {
        this.failIfClosed();
        this.surface.pause();
    };
    RenderedCameraImpl.prototype.resume = function(onResumeCallback) {
        this.failIfClosed();
        var $this = this;
        var onVideoResume = function() {
            setTimeout(onResumeCallback, 200);
            $this.surface.removeEventListener("playing", onVideoResume);
        };
        this.surface.addEventListener("playing", onVideoResume);
        this.surface.play();
    };
    RenderedCameraImpl.prototype.isPaused = function() {
        this.failIfClosed();
        return this.surface.paused;
    };
    RenderedCameraImpl.prototype.getSurface = function() {
        this.failIfClosed();
        return this.surface;
    };
    RenderedCameraImpl.prototype.getRunningTrackCapabilities = function() {
        return this.getFirstTrackOrFail().getCapabilities();
    };
    RenderedCameraImpl.prototype.getRunningTrackSettings = function() {
        return this.getFirstTrackOrFail().getSettings();
    };
    RenderedCameraImpl.prototype.applyVideoConstraints = function(constraints) {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                if ("aspectRatio" in constraints) {
                    throw "Changing 'aspectRatio' in run-time is not yet supported.";
                }
                return [
                    2,
                    this.getFirstTrackOrFail().applyConstraints(constraints)
                ];
            });
        });
    };
    RenderedCameraImpl.prototype.close = function() {
        if (this.isClosed) {
            return Promise.resolve();
        }
        var $this = this;
        return new Promise(function(resolve, _) {
            var tracks = $this.mediaStream.getVideoTracks();
            var tracksToClose = tracks.length;
            var tracksClosed = 0;
            $this.mediaStream.getVideoTracks().forEach(function(videoTrack) {
                $this.mediaStream.removeTrack(videoTrack);
                videoTrack.stop();
                ++tracksClosed;
                if (tracksClosed >= tracksToClose) {
                    $this.isClosed = true;
                    $this.parentElement.removeChild($this.surface);
                    resolve();
                }
            });
        });
    };
    RenderedCameraImpl.prototype.getCapabilities = function() {
        return new CameraCapabilitiesImpl(this.getFirstTrackOrFail());
    };
    return RenderedCameraImpl;
}();
var CameraImpl = function() {
    function CameraImpl(mediaStream) {
        this.mediaStream = mediaStream;
    }
    CameraImpl.prototype.render = function(parentElement, options, callbacks) {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                return [
                    2,
                    RenderedCameraImpl.create(parentElement, this.mediaStream, options, callbacks)
                ];
            });
        });
    };
    CameraImpl.create = function(videoConstraints) {
        return __awaiter(this, void 0, void 0, function() {
            var constraints, mediaStream;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        if (!navigator.mediaDevices) {
                            throw "navigator.mediaDevices not supported";
                        }
                        constraints = {
                            audio: false,
                            video: videoConstraints
                        };
                        return [
                            4,
                            navigator.mediaDevices.getUserMedia(constraints)
                        ];
                    case 1:
                        mediaStream = _a.sent();
                        return [
                            2,
                            new CameraImpl(mediaStream)
                        ];
                }
            });
        });
    };
    return CameraImpl;
}();
;
 //# sourceMappingURL=core-impl.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/camera/factories.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CameraFactory": (()=>CameraFactory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$core$2d$impl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/camera/core-impl.js [app-client] (ecmascript)");
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
;
var CameraFactory = function() {
    function CameraFactory() {}
    CameraFactory.failIfNotSupported = function() {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                if (!navigator.mediaDevices) {
                    throw "navigator.mediaDevices not supported";
                }
                return [
                    2,
                    new CameraFactory()
                ];
            });
        });
    };
    CameraFactory.prototype.create = function(videoConstraints) {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                return [
                    2,
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$core$2d$impl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CameraImpl"].create(videoConstraints)
                ];
            });
        });
    };
    return CameraFactory;
}();
;
 //# sourceMappingURL=factories.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/camera/retriever.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CameraRetriever": (()=>CameraRetriever)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)");
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
;
var CameraRetriever = function() {
    function CameraRetriever() {}
    CameraRetriever.retrieve = function() {
        if (navigator.mediaDevices) {
            return CameraRetriever.getCamerasFromMediaDevices();
        }
        var mst = MediaStreamTrack;
        if (MediaStreamTrack && mst.getSources) {
            return CameraRetriever.getCamerasFromMediaStreamTrack();
        }
        return CameraRetriever.rejectWithError();
    };
    CameraRetriever.rejectWithError = function() {
        var errorMessage = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeStrings"].unableToQuerySupportedDevices();
        if (!CameraRetriever.isHttpsOrLocalhost()) {
            errorMessage = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeStrings"].insecureContextCameraQueryError();
        }
        return Promise.reject(errorMessage);
    };
    CameraRetriever.isHttpsOrLocalhost = function() {
        if (location.protocol === "https:") {
            return true;
        }
        var host = location.host.split(":")[0];
        return host === "127.0.0.1" || host === "localhost";
    };
    CameraRetriever.getCamerasFromMediaDevices = function() {
        return __awaiter(this, void 0, void 0, function() {
            var closeActiveStreams, mediaStream, devices, results, _i, devices_1, device;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        closeActiveStreams = function(stream) {
                            var tracks = stream.getVideoTracks();
                            for(var _i = 0, tracks_1 = tracks; _i < tracks_1.length; _i++){
                                var track = tracks_1[_i];
                                track.enabled = false;
                                track.stop();
                                stream.removeTrack(track);
                            }
                        };
                        return [
                            4,
                            navigator.mediaDevices.getUserMedia({
                                audio: false,
                                video: true
                            })
                        ];
                    case 1:
                        mediaStream = _a.sent();
                        return [
                            4,
                            navigator.mediaDevices.enumerateDevices()
                        ];
                    case 2:
                        devices = _a.sent();
                        results = [];
                        for(_i = 0, devices_1 = devices; _i < devices_1.length; _i++){
                            device = devices_1[_i];
                            if (device.kind === "videoinput") {
                                results.push({
                                    id: device.deviceId,
                                    label: device.label
                                });
                            }
                        }
                        closeActiveStreams(mediaStream);
                        return [
                            2,
                            results
                        ];
                }
            });
        });
    };
    CameraRetriever.getCamerasFromMediaStreamTrack = function() {
        return new Promise(function(resolve, _) {
            var callback = function(sourceInfos) {
                var results = [];
                for(var _i = 0, sourceInfos_1 = sourceInfos; _i < sourceInfos_1.length; _i++){
                    var sourceInfo = sourceInfos_1[_i];
                    if (sourceInfo.kind === "video") {
                        results.push({
                            id: sourceInfo.id,
                            label: sourceInfo.label
                        });
                    }
                }
                resolve(results);
            };
            var mst = MediaStreamTrack;
            mst.getSources(callback);
        });
    };
    return CameraRetriever;
}();
;
 //# sourceMappingURL=retriever.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/state-manager.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html5QrcodeScannerState": (()=>Html5QrcodeScannerState),
    "StateManagerFactory": (()=>StateManagerFactory),
    "StateManagerProxy": (()=>StateManagerProxy)
});
var Html5QrcodeScannerState;
(function(Html5QrcodeScannerState) {
    Html5QrcodeScannerState[Html5QrcodeScannerState["UNKNOWN"] = 0] = "UNKNOWN";
    Html5QrcodeScannerState[Html5QrcodeScannerState["NOT_STARTED"] = 1] = "NOT_STARTED";
    Html5QrcodeScannerState[Html5QrcodeScannerState["SCANNING"] = 2] = "SCANNING";
    Html5QrcodeScannerState[Html5QrcodeScannerState["PAUSED"] = 3] = "PAUSED";
})(Html5QrcodeScannerState || (Html5QrcodeScannerState = {}));
var StateManagerImpl = function() {
    function StateManagerImpl() {
        this.state = Html5QrcodeScannerState.NOT_STARTED;
        this.onGoingTransactionNewState = Html5QrcodeScannerState.UNKNOWN;
    }
    StateManagerImpl.prototype.directTransition = function(newState) {
        this.failIfTransitionOngoing();
        this.validateTransition(newState);
        this.state = newState;
    };
    StateManagerImpl.prototype.startTransition = function(newState) {
        this.failIfTransitionOngoing();
        this.validateTransition(newState);
        this.onGoingTransactionNewState = newState;
        return this;
    };
    StateManagerImpl.prototype.execute = function() {
        if (this.onGoingTransactionNewState === Html5QrcodeScannerState.UNKNOWN) {
            throw "Transaction is already cancelled, cannot execute().";
        }
        var tempNewState = this.onGoingTransactionNewState;
        this.onGoingTransactionNewState = Html5QrcodeScannerState.UNKNOWN;
        this.directTransition(tempNewState);
    };
    StateManagerImpl.prototype.cancel = function() {
        if (this.onGoingTransactionNewState === Html5QrcodeScannerState.UNKNOWN) {
            throw "Transaction is already cancelled, cannot cancel().";
        }
        this.onGoingTransactionNewState = Html5QrcodeScannerState.UNKNOWN;
    };
    StateManagerImpl.prototype.getState = function() {
        return this.state;
    };
    StateManagerImpl.prototype.failIfTransitionOngoing = function() {
        if (this.onGoingTransactionNewState !== Html5QrcodeScannerState.UNKNOWN) {
            throw "Cannot transition to a new state, already under transition";
        }
    };
    StateManagerImpl.prototype.validateTransition = function(newState) {
        switch(this.state){
            case Html5QrcodeScannerState.UNKNOWN:
                throw "Transition from unknown is not allowed";
            case Html5QrcodeScannerState.NOT_STARTED:
                this.failIfNewStateIs(newState, [
                    Html5QrcodeScannerState.PAUSED
                ]);
                break;
            case Html5QrcodeScannerState.SCANNING:
                break;
            case Html5QrcodeScannerState.PAUSED:
                break;
        }
    };
    StateManagerImpl.prototype.failIfNewStateIs = function(newState, disallowedStatesToTransition) {
        for(var _i = 0, disallowedStatesToTransition_1 = disallowedStatesToTransition; _i < disallowedStatesToTransition_1.length; _i++){
            var disallowedState = disallowedStatesToTransition_1[_i];
            if (newState === disallowedState) {
                throw "Cannot transition from ".concat(this.state, " to ").concat(newState);
            }
        }
    };
    return StateManagerImpl;
}();
var StateManagerProxy = function() {
    function StateManagerProxy(stateManager) {
        this.stateManager = stateManager;
    }
    StateManagerProxy.prototype.startTransition = function(newState) {
        return this.stateManager.startTransition(newState);
    };
    StateManagerProxy.prototype.directTransition = function(newState) {
        this.stateManager.directTransition(newState);
    };
    StateManagerProxy.prototype.getState = function() {
        return this.stateManager.getState();
    };
    StateManagerProxy.prototype.canScanFile = function() {
        return this.stateManager.getState() === Html5QrcodeScannerState.NOT_STARTED;
    };
    StateManagerProxy.prototype.isScanning = function() {
        return this.stateManager.getState() !== Html5QrcodeScannerState.NOT_STARTED;
    };
    StateManagerProxy.prototype.isStrictlyScanning = function() {
        return this.stateManager.getState() === Html5QrcodeScannerState.SCANNING;
    };
    StateManagerProxy.prototype.isPaused = function() {
        return this.stateManager.getState() === Html5QrcodeScannerState.PAUSED;
    };
    return StateManagerProxy;
}();
;
var StateManagerFactory = function() {
    function StateManagerFactory() {}
    StateManagerFactory.create = function() {
        return new StateManagerProxy(new StateManagerImpl());
    };
    return StateManagerFactory;
}();
;
 //# sourceMappingURL=state-manager.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/html5-qrcode.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html5Qrcode": (()=>Html5Qrcode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$code$2d$decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/code-decoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$factories$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/camera/factories.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$retriever$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/camera/retriever.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$state$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/state-manager.js [app-client] (ecmascript)");
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
;
;
var Constants = function(_super) {
    __extends(Constants, _super);
    function Constants() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    Constants.DEFAULT_WIDTH = 300;
    Constants.DEFAULT_WIDTH_OFFSET = 2;
    Constants.FILE_SCAN_MIN_HEIGHT = 300;
    Constants.FILE_SCAN_HIDDEN_CANVAS_PADDING = 100;
    Constants.MIN_QR_BOX_SIZE = 50;
    Constants.SHADED_LEFT = 1;
    Constants.SHADED_RIGHT = 2;
    Constants.SHADED_TOP = 3;
    Constants.SHADED_BOTTOM = 4;
    Constants.SHADED_REGION_ELEMENT_ID = "qr-shaded-region";
    Constants.VERBOSE = false;
    Constants.BORDER_SHADER_DEFAULT_COLOR = "#ffffff";
    Constants.BORDER_SHADER_MATCH_COLOR = "rgb(90, 193, 56)";
    return Constants;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"]);
var InternalHtml5QrcodeConfig = function() {
    function InternalHtml5QrcodeConfig(config, logger) {
        this.logger = logger;
        this.fps = Constants.SCAN_DEFAULT_FPS;
        if (!config) {
            this.disableFlip = Constants.DEFAULT_DISABLE_FLIP;
        } else {
            if (config.fps) {
                this.fps = config.fps;
            }
            this.disableFlip = config.disableFlip === true;
            this.qrbox = config.qrbox;
            this.aspectRatio = config.aspectRatio;
            this.videoConstraints = config.videoConstraints;
        }
    }
    InternalHtml5QrcodeConfig.prototype.isMediaStreamConstraintsValid = function() {
        if (!this.videoConstraints) {
            this.logger.logError("Empty videoConstraints", true);
            return false;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VideoConstraintsUtil"].isMediaStreamConstraintsValid(this.videoConstraints, this.logger);
    };
    InternalHtml5QrcodeConfig.prototype.isShadedBoxEnabled = function() {
        return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(this.qrbox);
    };
    InternalHtml5QrcodeConfig.create = function(config, logger) {
        return new InternalHtml5QrcodeConfig(config, logger);
    };
    return InternalHtml5QrcodeConfig;
}();
var Html5Qrcode = function() {
    function Html5Qrcode(elementId, configOrVerbosityFlag) {
        this.element = null;
        this.canvasElement = null;
        this.scannerPausedUiElement = null;
        this.hasBorderShaders = null;
        this.borderShaders = null;
        this.qrMatch = null;
        this.renderedCamera = null;
        this.qrRegion = null;
        this.context = null;
        this.lastScanImageFile = null;
        this.isScanning = false;
        if (!document.getElementById(elementId)) {
            throw "HTML Element with id=".concat(elementId, " not found");
        }
        this.elementId = elementId;
        this.verbose = false;
        var experimentalFeatureConfig;
        var configObject;
        if (typeof configOrVerbosityFlag == "boolean") {
            this.verbose = configOrVerbosityFlag === true;
        } else if (configOrVerbosityFlag) {
            configObject = configOrVerbosityFlag;
            this.verbose = configObject.verbose === true;
            experimentalFeatureConfig = configObject.experimentalFeatures;
        }
        this.logger = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseLoggger"](this.verbose);
        this.qrcode = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$code$2d$decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeShim"](this.getSupportedFormats(configOrVerbosityFlag), this.getUseBarCodeDetectorIfSupported(configObject), this.verbose, this.logger);
        this.foreverScanTimeout;
        this.shouldScan = true;
        this.stateManagerProxy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$state$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StateManagerFactory"].create();
    }
    Html5Qrcode.prototype.start = function(cameraIdOrConfig, configuration, qrCodeSuccessCallback, qrCodeErrorCallback) {
        var _this = this;
        if (!cameraIdOrConfig) {
            throw "cameraIdOrConfig is required";
        }
        if (!qrCodeSuccessCallback || typeof qrCodeSuccessCallback != "function") {
            throw "qrCodeSuccessCallback is required and should be a function.";
        }
        var qrCodeErrorCallbackInternal;
        if (qrCodeErrorCallback) {
            qrCodeErrorCallbackInternal = qrCodeErrorCallback;
        } else {
            qrCodeErrorCallbackInternal = this.verbose ? this.logger.log : function() {};
        }
        var internalConfig = InternalHtml5QrcodeConfig.create(configuration, this.logger);
        this.clearElement();
        var videoConstraintsAvailableAndValid = false;
        if (internalConfig.videoConstraints) {
            if (!internalConfig.isMediaStreamConstraintsValid()) {
                this.logger.logError("'videoConstraints' is not valid 'MediaStreamConstraints, " + "it will be ignored.'", true);
            } else {
                videoConstraintsAvailableAndValid = true;
            }
        }
        var areVideoConstraintsEnabled = videoConstraintsAvailableAndValid;
        var element = document.getElementById(this.elementId);
        var rootElementWidth = element.clientWidth ? element.clientWidth : Constants.DEFAULT_WIDTH;
        element.style.position = "relative";
        this.shouldScan = true;
        this.element = element;
        var $this = this;
        var toScanningStateChangeTransaction = this.stateManagerProxy.startTransition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$state$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerState"].SCANNING);
        return new Promise(function(resolve, reject) {
            var videoConstraints = areVideoConstraintsEnabled ? internalConfig.videoConstraints : $this.createVideoConstraints(cameraIdOrConfig);
            if (!videoConstraints) {
                toScanningStateChangeTransaction.cancel();
                reject("videoConstraints should be defined");
                return;
            }
            var cameraRenderingOptions = {};
            if (!areVideoConstraintsEnabled || internalConfig.aspectRatio) {
                cameraRenderingOptions.aspectRatio = internalConfig.aspectRatio;
            }
            var renderingCallbacks = {
                onRenderSurfaceReady: function(viewfinderWidth, viewfinderHeight) {
                    $this.setupUi(viewfinderWidth, viewfinderHeight, internalConfig);
                    $this.isScanning = true;
                    $this.foreverScan(internalConfig, qrCodeSuccessCallback, qrCodeErrorCallbackInternal);
                }
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$factories$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CameraFactory"].failIfNotSupported().then(function(factory) {
                factory.create(videoConstraints).then(function(camera) {
                    return camera.render(_this.element, cameraRenderingOptions, renderingCallbacks).then(function(renderedCamera) {
                        $this.renderedCamera = renderedCamera;
                        toScanningStateChangeTransaction.execute();
                        resolve(null);
                    }).catch(function(error) {
                        toScanningStateChangeTransaction.cancel();
                        reject(error);
                    });
                }).catch(function(error) {
                    toScanningStateChangeTransaction.cancel();
                    reject(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeStrings"].errorGettingUserMedia(error));
                });
            }).catch(function(_) {
                toScanningStateChangeTransaction.cancel();
                reject(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeStrings"].cameraStreamingNotSupported());
            });
        });
    };
    Html5Qrcode.prototype.pause = function(shouldPauseVideo) {
        if (!this.stateManagerProxy.isStrictlyScanning()) {
            throw "Cannot pause, scanner is not scanning.";
        }
        this.stateManagerProxy.directTransition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$state$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerState"].PAUSED);
        this.showPausedState();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(shouldPauseVideo) || shouldPauseVideo !== true) {
            shouldPauseVideo = false;
        }
        if (shouldPauseVideo && this.renderedCamera) {
            this.renderedCamera.pause();
        }
    };
    Html5Qrcode.prototype.resume = function() {
        if (!this.stateManagerProxy.isPaused()) {
            throw "Cannot result, scanner is not paused.";
        }
        if (!this.renderedCamera) {
            throw "renderedCamera doesn't exist while trying resume()";
        }
        var $this = this;
        var transitionToScanning = function() {
            $this.stateManagerProxy.directTransition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$state$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerState"].SCANNING);
            $this.hidePausedState();
        };
        if (!this.renderedCamera.isPaused()) {
            transitionToScanning();
            return;
        }
        this.renderedCamera.resume(function() {
            transitionToScanning();
        });
    };
    Html5Qrcode.prototype.getState = function() {
        return this.stateManagerProxy.getState();
    };
    Html5Qrcode.prototype.stop = function() {
        var _this = this;
        if (!this.stateManagerProxy.isScanning()) {
            throw "Cannot stop, scanner is not running or paused.";
        }
        var toStoppedStateTransaction = this.stateManagerProxy.startTransition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$state$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerState"].NOT_STARTED);
        this.shouldScan = false;
        if (this.foreverScanTimeout) {
            clearTimeout(this.foreverScanTimeout);
        }
        var removeQrRegion = function() {
            if (!_this.element) {
                return;
            }
            var childElement = document.getElementById(Constants.SHADED_REGION_ELEMENT_ID);
            if (childElement) {
                _this.element.removeChild(childElement);
            }
        };
        var $this = this;
        return this.renderedCamera.close().then(function() {
            $this.renderedCamera = null;
            if ($this.element) {
                $this.element.removeChild($this.canvasElement);
                $this.canvasElement = null;
            }
            removeQrRegion();
            if ($this.qrRegion) {
                $this.qrRegion = null;
            }
            if ($this.context) {
                $this.context = null;
            }
            toStoppedStateTransaction.execute();
            $this.hidePausedState();
            $this.isScanning = false;
            return Promise.resolve();
        });
    };
    Html5Qrcode.prototype.scanFile = function(imageFile, showImage) {
        return this.scanFileV2(imageFile, showImage).then(function(html5qrcodeResult) {
            return html5qrcodeResult.decodedText;
        });
    };
    Html5Qrcode.prototype.scanFileV2 = function(imageFile, showImage) {
        var _this = this;
        if (!imageFile || !(imageFile instanceof File)) {
            throw "imageFile argument is mandatory and should be instance " + "of File. Use 'event.target.files[0]'.";
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(showImage)) {
            showImage = true;
        }
        if (!this.stateManagerProxy.canScanFile()) {
            throw "Cannot start file scan - ongoing camera scan";
        }
        return new Promise(function(resolve, reject) {
            _this.possiblyCloseLastScanImageFile();
            _this.clearElement();
            _this.lastScanImageFile = URL.createObjectURL(imageFile);
            var inputImage = new Image;
            inputImage.onload = function() {
                var imageWidth = inputImage.width;
                var imageHeight = inputImage.height;
                var element = document.getElementById(_this.elementId);
                var containerWidth = element.clientWidth ? element.clientWidth : Constants.DEFAULT_WIDTH;
                var containerHeight = Math.max(element.clientHeight ? element.clientHeight : imageHeight, Constants.FILE_SCAN_MIN_HEIGHT);
                var config = _this.computeCanvasDrawConfig(imageWidth, imageHeight, containerWidth, containerHeight);
                if (showImage) {
                    var visibleCanvas = _this.createCanvasElement(containerWidth, containerHeight, "qr-canvas-visible");
                    visibleCanvas.style.display = "inline-block";
                    element.appendChild(visibleCanvas);
                    var context_1 = visibleCanvas.getContext("2d");
                    if (!context_1) {
                        throw "Unable to get 2d context from canvas";
                    }
                    context_1.canvas.width = containerWidth;
                    context_1.canvas.height = containerHeight;
                    context_1.drawImage(inputImage, 0, 0, imageWidth, imageHeight, config.x, config.y, config.width, config.height);
                }
                var padding = Constants.FILE_SCAN_HIDDEN_CANVAS_PADDING;
                var hiddenImageWidth = Math.max(inputImage.width, config.width);
                var hiddenImageHeight = Math.max(inputImage.height, config.height);
                var hiddenCanvasWidth = hiddenImageWidth + 2 * padding;
                var hiddenCanvasHeight = hiddenImageHeight + 2 * padding;
                var hiddenCanvas = _this.createCanvasElement(hiddenCanvasWidth, hiddenCanvasHeight);
                element.appendChild(hiddenCanvas);
                var context = hiddenCanvas.getContext("2d");
                if (!context) {
                    throw "Unable to get 2d context from canvas";
                }
                context.canvas.width = hiddenCanvasWidth;
                context.canvas.height = hiddenCanvasHeight;
                context.drawImage(inputImage, 0, 0, imageWidth, imageHeight, padding, padding, hiddenImageWidth, hiddenImageHeight);
                try {
                    _this.qrcode.decodeRobustlyAsync(hiddenCanvas).then(function(result) {
                        resolve(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeResultFactory"].createFromQrcodeResult(result));
                    }).catch(reject);
                } catch (exception) {
                    reject("QR code parse error, error = ".concat(exception));
                }
            };
            inputImage.onerror = reject;
            inputImage.onabort = reject;
            inputImage.onstalled = reject;
            inputImage.onsuspend = reject;
            inputImage.src = URL.createObjectURL(imageFile);
        });
    };
    Html5Qrcode.prototype.clear = function() {
        this.clearElement();
    };
    Html5Qrcode.getCameras = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$retriever$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CameraRetriever"].retrieve();
    };
    Html5Qrcode.prototype.getRunningTrackCapabilities = function() {
        return this.getRenderedCameraOrFail().getRunningTrackCapabilities();
    };
    Html5Qrcode.prototype.getRunningTrackSettings = function() {
        return this.getRenderedCameraOrFail().getRunningTrackSettings();
    };
    Html5Qrcode.prototype.getRunningTrackCameraCapabilities = function() {
        return this.getRenderedCameraOrFail().getCapabilities();
    };
    Html5Qrcode.prototype.applyVideoConstraints = function(videoConstaints) {
        if (!videoConstaints) {
            throw "videoConstaints is required argument.";
        } else if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VideoConstraintsUtil"].isMediaStreamConstraintsValid(videoConstaints, this.logger)) {
            throw "invalid videoConstaints passed, check logs for more details";
        }
        return this.getRenderedCameraOrFail().applyVideoConstraints(videoConstaints);
    };
    Html5Qrcode.prototype.getRenderedCameraOrFail = function() {
        if (this.renderedCamera == null) {
            throw "Scanning is not in running state, call this API only when" + " QR code scanning using camera is in running state.";
        }
        return this.renderedCamera;
    };
    Html5Qrcode.prototype.getSupportedFormats = function(configOrVerbosityFlag) {
        var allFormats = [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].QR_CODE,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].AZTEC,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODABAR,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_39,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_93,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].CODE_128,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].DATA_MATRIX,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].MAXICODE,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].ITF,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].EAN_13,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].EAN_8,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].PDF_417,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].RSS_14,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].RSS_EXPANDED,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].UPC_A,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].UPC_E,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeSupportedFormats"].UPC_EAN_EXTENSION
        ];
        if (!configOrVerbosityFlag || typeof configOrVerbosityFlag == "boolean") {
            return allFormats;
        }
        if (!configOrVerbosityFlag.formatsToSupport) {
            return allFormats;
        }
        if (!Array.isArray(configOrVerbosityFlag.formatsToSupport)) {
            throw "configOrVerbosityFlag.formatsToSupport should be undefined " + "or an array.";
        }
        if (configOrVerbosityFlag.formatsToSupport.length === 0) {
            throw "Atleast 1 formatsToSupport is needed.";
        }
        var supportedFormats = [];
        for(var _i = 0, _a = configOrVerbosityFlag.formatsToSupport; _i < _a.length; _i++){
            var format = _a[_i];
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidHtml5QrcodeSupportedFormats"])(format)) {
                supportedFormats.push(format);
            } else {
                this.logger.warn("Invalid format: ".concat(format, " passed in config, ignoring."));
            }
        }
        if (supportedFormats.length === 0) {
            throw "None of formatsToSupport match supported values.";
        }
        return supportedFormats;
    };
    Html5Qrcode.prototype.getUseBarCodeDetectorIfSupported = function(config) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(config)) {
            return true;
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(config.useBarCodeDetectorIfSupported)) {
            return config.useBarCodeDetectorIfSupported !== false;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(config.experimentalFeatures)) {
            return true;
        }
        var experimentalFeatures = config.experimentalFeatures;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(experimentalFeatures.useBarCodeDetectorIfSupported)) {
            return true;
        }
        return experimentalFeatures.useBarCodeDetectorIfSupported !== false;
    };
    Html5Qrcode.prototype.validateQrboxSize = function(viewfinderWidth, viewfinderHeight, internalConfig) {
        var _this = this;
        var qrboxSize = internalConfig.qrbox;
        this.validateQrboxConfig(qrboxSize);
        var qrDimensions = this.toQrdimensions(viewfinderWidth, viewfinderHeight, qrboxSize);
        var validateMinSize = function(size) {
            if (size < Constants.MIN_QR_BOX_SIZE) {
                throw "minimum size of 'config.qrbox' dimension value is" + " ".concat(Constants.MIN_QR_BOX_SIZE, "px.");
            }
        };
        var correctWidthBasedOnRootElementSize = function(configWidth) {
            if (configWidth > viewfinderWidth) {
                _this.logger.warn("`qrbox.width` or `qrbox` is larger than the" + " width of the root element. The width will be truncated" + " to the width of root element.");
                configWidth = viewfinderWidth;
            }
            return configWidth;
        };
        validateMinSize(qrDimensions.width);
        validateMinSize(qrDimensions.height);
        qrDimensions.width = correctWidthBasedOnRootElementSize(qrDimensions.width);
    };
    Html5Qrcode.prototype.validateQrboxConfig = function(qrboxSize) {
        if (typeof qrboxSize === "number") {
            return;
        }
        if (typeof qrboxSize === "function") {
            return;
        }
        if (qrboxSize.width === undefined || qrboxSize.height === undefined) {
            throw "Invalid instance of QrDimensions passed for " + "'config.qrbox'. Both 'width' and 'height' should be set.";
        }
    };
    Html5Qrcode.prototype.toQrdimensions = function(viewfinderWidth, viewfinderHeight, qrboxSize) {
        if (typeof qrboxSize === "number") {
            return {
                width: qrboxSize,
                height: qrboxSize
            };
        } else if (typeof qrboxSize === "function") {
            try {
                return qrboxSize(viewfinderWidth, viewfinderHeight);
            } catch (error) {
                throw new Error("qrbox config was passed as a function but it failed with " + "unknown error" + error);
            }
        }
        return qrboxSize;
    };
    Html5Qrcode.prototype.setupUi = function(viewfinderWidth, viewfinderHeight, internalConfig) {
        if (internalConfig.isShadedBoxEnabled()) {
            this.validateQrboxSize(viewfinderWidth, viewfinderHeight, internalConfig);
        }
        var qrboxSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(internalConfig.qrbox) ? {
            width: viewfinderWidth,
            height: viewfinderHeight
        } : internalConfig.qrbox;
        this.validateQrboxConfig(qrboxSize);
        var qrDimensions = this.toQrdimensions(viewfinderWidth, viewfinderHeight, qrboxSize);
        if (qrDimensions.height > viewfinderHeight) {
            this.logger.warn("[Html5Qrcode] config.qrbox has height that is" + "greater than the height of the video stream. Shading will be" + " ignored");
        }
        var shouldShadingBeApplied = internalConfig.isShadedBoxEnabled() && qrDimensions.height <= viewfinderHeight;
        var defaultQrRegion = {
            x: 0,
            y: 0,
            width: viewfinderWidth,
            height: viewfinderHeight
        };
        var qrRegion = shouldShadingBeApplied ? this.getShadedRegionBounds(viewfinderWidth, viewfinderHeight, qrDimensions) : defaultQrRegion;
        var canvasElement = this.createCanvasElement(qrRegion.width, qrRegion.height);
        var contextAttributes = {
            willReadFrequently: true
        };
        var context = canvasElement.getContext("2d", contextAttributes);
        context.canvas.width = qrRegion.width;
        context.canvas.height = qrRegion.height;
        this.element.append(canvasElement);
        if (shouldShadingBeApplied) {
            this.possiblyInsertShadingElement(this.element, viewfinderWidth, viewfinderHeight, qrDimensions);
        }
        this.createScannerPausedUiElement(this.element);
        this.qrRegion = qrRegion;
        this.context = context;
        this.canvasElement = canvasElement;
    };
    Html5Qrcode.prototype.createScannerPausedUiElement = function(rootElement) {
        var scannerPausedUiElement = document.createElement("div");
        scannerPausedUiElement.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeStrings"].scannerPaused();
        scannerPausedUiElement.style.display = "none";
        scannerPausedUiElement.style.position = "absolute";
        scannerPausedUiElement.style.top = "0px";
        scannerPausedUiElement.style.zIndex = "1";
        scannerPausedUiElement.style.background = "rgba(9, 9, 9, 0.46)";
        scannerPausedUiElement.style.color = "#FFECEC";
        scannerPausedUiElement.style.textAlign = "center";
        scannerPausedUiElement.style.width = "100%";
        rootElement.appendChild(scannerPausedUiElement);
        this.scannerPausedUiElement = scannerPausedUiElement;
    };
    Html5Qrcode.prototype.scanContext = function(qrCodeSuccessCallback, qrCodeErrorCallback) {
        var _this = this;
        if (this.stateManagerProxy.isPaused()) {
            return Promise.resolve(false);
        }
        return this.qrcode.decodeAsync(this.canvasElement).then(function(result) {
            qrCodeSuccessCallback(result.text, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeResultFactory"].createFromQrcodeResult(result));
            _this.possiblyUpdateShaders(true);
            return true;
        }).catch(function(error) {
            _this.possiblyUpdateShaders(false);
            var errorMessage = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeStrings"].codeParseError(error);
            qrCodeErrorCallback(errorMessage, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeErrorFactory"].createFrom(errorMessage));
            return false;
        });
    };
    Html5Qrcode.prototype.foreverScan = function(internalConfig, qrCodeSuccessCallback, qrCodeErrorCallback) {
        var _this = this;
        if (!this.shouldScan) {
            return;
        }
        if (!this.renderedCamera) {
            return;
        }
        var videoElement = this.renderedCamera.getSurface();
        var widthRatio = videoElement.videoWidth / videoElement.clientWidth;
        var heightRatio = videoElement.videoHeight / videoElement.clientHeight;
        if (!this.qrRegion) {
            throw "qrRegion undefined when localMediaStream is ready.";
        }
        var sWidthOffset = this.qrRegion.width * widthRatio;
        var sHeightOffset = this.qrRegion.height * heightRatio;
        var sxOffset = this.qrRegion.x * widthRatio;
        var syOffset = this.qrRegion.y * heightRatio;
        this.context.drawImage(videoElement, sxOffset, syOffset, sWidthOffset, sHeightOffset, 0, 0, this.qrRegion.width, this.qrRegion.height);
        var triggerNextScan = function() {
            _this.foreverScanTimeout = setTimeout(function() {
                _this.foreverScan(internalConfig, qrCodeSuccessCallback, qrCodeErrorCallback);
            }, _this.getTimeoutFps(internalConfig.fps));
        };
        this.scanContext(qrCodeSuccessCallback, qrCodeErrorCallback).then(function(isSuccessfull) {
            if (!isSuccessfull && internalConfig.disableFlip !== true) {
                _this.context.translate(_this.context.canvas.width, 0);
                _this.context.scale(-1, 1);
                _this.scanContext(qrCodeSuccessCallback, qrCodeErrorCallback).finally(function() {
                    triggerNextScan();
                });
            } else {
                triggerNextScan();
            }
        }).catch(function(error) {
            _this.logger.logError("Error happend while scanning context", error);
            triggerNextScan();
        });
    };
    Html5Qrcode.prototype.createVideoConstraints = function(cameraIdOrConfig) {
        if (typeof cameraIdOrConfig == "string") {
            return {
                deviceId: {
                    exact: cameraIdOrConfig
                }
            };
        } else if (typeof cameraIdOrConfig == "object") {
            var facingModeKey = "facingMode";
            var deviceIdKey = "deviceId";
            var allowedFacingModeValues_1 = {
                "user": true,
                "environment": true
            };
            var exactKey = "exact";
            var isValidFacingModeValue = function(value) {
                if (value in allowedFacingModeValues_1) {
                    return true;
                } else {
                    throw "config has invalid 'facingMode' value = " + "'".concat(value, "'");
                }
            };
            var keys = Object.keys(cameraIdOrConfig);
            if (keys.length !== 1) {
                throw "'cameraIdOrConfig' object should have exactly 1 key," + " if passed as an object, found ".concat(keys.length, " keys");
            }
            var key = Object.keys(cameraIdOrConfig)[0];
            if (key !== facingModeKey && key !== deviceIdKey) {
                throw "Only '".concat(facingModeKey, "' and '").concat(deviceIdKey, "' ") + " are supported for 'cameraIdOrConfig'";
            }
            if (key === facingModeKey) {
                var facingMode = cameraIdOrConfig.facingMode;
                if (typeof facingMode == "string") {
                    if (isValidFacingModeValue(facingMode)) {
                        return {
                            facingMode: facingMode
                        };
                    }
                } else if (typeof facingMode == "object") {
                    if (exactKey in facingMode) {
                        if (isValidFacingModeValue(facingMode["".concat(exactKey)])) {
                            return {
                                facingMode: {
                                    exact: facingMode["".concat(exactKey)]
                                }
                            };
                        }
                    } else {
                        throw "'facingMode' should be string or object with" + " ".concat(exactKey, " as key.");
                    }
                } else {
                    var type_1 = typeof facingMode;
                    throw "Invalid type of 'facingMode' = ".concat(type_1);
                }
            } else {
                var deviceId = cameraIdOrConfig.deviceId;
                if (typeof deviceId == "string") {
                    return {
                        deviceId: deviceId
                    };
                } else if (typeof deviceId == "object") {
                    if (exactKey in deviceId) {
                        return {
                            deviceId: {
                                exact: deviceId["".concat(exactKey)]
                            }
                        };
                    } else {
                        throw "'deviceId' should be string or object with" + " ".concat(exactKey, " as key.");
                    }
                } else {
                    var type_2 = typeof deviceId;
                    throw "Invalid type of 'deviceId' = ".concat(type_2);
                }
            }
        }
        var type = typeof cameraIdOrConfig;
        throw "Invalid type of 'cameraIdOrConfig' = ".concat(type);
    };
    Html5Qrcode.prototype.computeCanvasDrawConfig = function(imageWidth, imageHeight, containerWidth, containerHeight) {
        if (imageWidth <= containerWidth && imageHeight <= containerHeight) {
            var xoffset = (containerWidth - imageWidth) / 2;
            var yoffset = (containerHeight - imageHeight) / 2;
            return {
                x: xoffset,
                y: yoffset,
                width: imageWidth,
                height: imageHeight
            };
        } else {
            var formerImageWidth = imageWidth;
            var formerImageHeight = imageHeight;
            if (imageWidth > containerWidth) {
                imageHeight = containerWidth / imageWidth * imageHeight;
                imageWidth = containerWidth;
            }
            if (imageHeight > containerHeight) {
                imageWidth = containerHeight / imageHeight * imageWidth;
                imageHeight = containerHeight;
            }
            this.logger.log("Image downsampled from " + "".concat(formerImageWidth, "X").concat(formerImageHeight) + " to ".concat(imageWidth, "X").concat(imageHeight, "."));
            return this.computeCanvasDrawConfig(imageWidth, imageHeight, containerWidth, containerHeight);
        }
    };
    Html5Qrcode.prototype.clearElement = function() {
        if (this.stateManagerProxy.isScanning()) {
            throw "Cannot clear while scan is ongoing, close it first.";
        }
        var element = document.getElementById(this.elementId);
        if (element) {
            element.innerHTML = "";
        }
    };
    Html5Qrcode.prototype.possiblyUpdateShaders = function(qrMatch) {
        if (this.qrMatch === qrMatch) {
            return;
        }
        if (this.hasBorderShaders && this.borderShaders && this.borderShaders.length) {
            this.borderShaders.forEach(function(shader) {
                shader.style.backgroundColor = qrMatch ? Constants.BORDER_SHADER_MATCH_COLOR : Constants.BORDER_SHADER_DEFAULT_COLOR;
            });
        }
        this.qrMatch = qrMatch;
    };
    Html5Qrcode.prototype.possiblyCloseLastScanImageFile = function() {
        if (this.lastScanImageFile) {
            URL.revokeObjectURL(this.lastScanImageFile);
            this.lastScanImageFile = null;
        }
    };
    Html5Qrcode.prototype.createCanvasElement = function(width, height, customId) {
        var canvasWidth = width;
        var canvasHeight = height;
        var canvasElement = document.createElement("canvas");
        canvasElement.style.width = "".concat(canvasWidth, "px");
        canvasElement.style.height = "".concat(canvasHeight, "px");
        canvasElement.style.display = "none";
        canvasElement.id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(customId) ? "qr-canvas" : customId;
        return canvasElement;
    };
    Html5Qrcode.prototype.getShadedRegionBounds = function(width, height, qrboxSize) {
        if (qrboxSize.width > width || qrboxSize.height > height) {
            throw "'config.qrbox' dimensions should not be greater than the " + "dimensions of the root HTML element.";
        }
        return {
            x: (width - qrboxSize.width) / 2,
            y: (height - qrboxSize.height) / 2,
            width: qrboxSize.width,
            height: qrboxSize.height
        };
    };
    Html5Qrcode.prototype.possiblyInsertShadingElement = function(element, width, height, qrboxSize) {
        if (width - qrboxSize.width < 1 || height - qrboxSize.height < 1) {
            return;
        }
        var shadingElement = document.createElement("div");
        shadingElement.style.position = "absolute";
        var rightLeftBorderSize = (width - qrboxSize.width) / 2;
        var topBottomBorderSize = (height - qrboxSize.height) / 2;
        shadingElement.style.borderLeft = "".concat(rightLeftBorderSize, "px solid rgba(0, 0, 0, 0.48)");
        shadingElement.style.borderRight = "".concat(rightLeftBorderSize, "px solid rgba(0, 0, 0, 0.48)");
        shadingElement.style.borderTop = "".concat(topBottomBorderSize, "px solid rgba(0, 0, 0, 0.48)");
        shadingElement.style.borderBottom = "".concat(topBottomBorderSize, "px solid rgba(0, 0, 0, 0.48)");
        shadingElement.style.boxSizing = "border-box";
        shadingElement.style.top = "0px";
        shadingElement.style.bottom = "0px";
        shadingElement.style.left = "0px";
        shadingElement.style.right = "0px";
        shadingElement.id = "".concat(Constants.SHADED_REGION_ELEMENT_ID);
        if (width - qrboxSize.width < 11 || height - qrboxSize.height < 11) {
            this.hasBorderShaders = false;
        } else {
            var smallSize = 5;
            var largeSize = 40;
            this.insertShaderBorders(shadingElement, largeSize, smallSize, -smallSize, null, 0, true);
            this.insertShaderBorders(shadingElement, largeSize, smallSize, -smallSize, null, 0, false);
            this.insertShaderBorders(shadingElement, largeSize, smallSize, null, -smallSize, 0, true);
            this.insertShaderBorders(shadingElement, largeSize, smallSize, null, -smallSize, 0, false);
            this.insertShaderBorders(shadingElement, smallSize, largeSize + smallSize, -smallSize, null, -smallSize, true);
            this.insertShaderBorders(shadingElement, smallSize, largeSize + smallSize, null, -smallSize, -smallSize, true);
            this.insertShaderBorders(shadingElement, smallSize, largeSize + smallSize, -smallSize, null, -smallSize, false);
            this.insertShaderBorders(shadingElement, smallSize, largeSize + smallSize, null, -smallSize, -smallSize, false);
            this.hasBorderShaders = true;
        }
        element.append(shadingElement);
    };
    Html5Qrcode.prototype.insertShaderBorders = function(shaderElem, width, height, top, bottom, side, isLeft) {
        var elem = document.createElement("div");
        elem.style.position = "absolute";
        elem.style.backgroundColor = Constants.BORDER_SHADER_DEFAULT_COLOR;
        elem.style.width = "".concat(width, "px");
        elem.style.height = "".concat(height, "px");
        if (top !== null) {
            elem.style.top = "".concat(top, "px");
        }
        if (bottom !== null) {
            elem.style.bottom = "".concat(bottom, "px");
        }
        if (isLeft) {
            elem.style.left = "".concat(side, "px");
        } else {
            elem.style.right = "".concat(side, "px");
        }
        if (!this.borderShaders) {
            this.borderShaders = [];
        }
        this.borderShaders.push(elem);
        shaderElem.appendChild(elem);
    };
    Html5Qrcode.prototype.showPausedState = function() {
        if (!this.scannerPausedUiElement) {
            throw "[internal error] scanner paused UI element not found";
        }
        this.scannerPausedUiElement.style.display = "block";
    };
    Html5Qrcode.prototype.hidePausedState = function() {
        if (!this.scannerPausedUiElement) {
            throw "[internal error] scanner paused UI element not found";
        }
        this.scannerPausedUiElement.style.display = "none";
    };
    Html5Qrcode.prototype.getTimeoutFps = function(fps) {
        return 1000 / fps;
    };
    return Html5Qrcode;
}();
;
 //# sourceMappingURL=html5-qrcode.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/image-assets.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ASSET_CAMERA_SCAN": (()=>ASSET_CAMERA_SCAN),
    "ASSET_CLOSE_ICON_16PX": (()=>ASSET_CLOSE_ICON_16PX),
    "ASSET_FILE_SCAN": (()=>ASSET_FILE_SCAN),
    "ASSET_INFO_ICON_16PX": (()=>ASSET_INFO_ICON_16PX)
});
var SVG_XML_PREFIX = "data:image/svg+xml;base64,";
var ASSET_CAMERA_SCAN = SVG_XML_PREFIX + "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";
var ASSET_FILE_SCAN = SVG_XML_PREFIX + "PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1OS4wMTggNTkuMDE4IiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1OS4wMTggNTkuMDE4IiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBkPSJtNTguNzQxIDU0LjgwOS01Ljk2OS02LjI0NGExMC43NCAxMC43NCAwIDAgMCAyLjgyLTcuMjVjMC01Ljk1My00Ljg0My0xMC43OTYtMTAuNzk2LTEwLjc5NlMzNCAzNS4zNjEgMzQgNDEuMzE0IDM4Ljg0MyA1Mi4xMSA0NC43OTYgNTIuMTFjMi40NDEgMCA0LjY4OC0uODI0IDYuNDk5LTIuMTk2bDYuMDAxIDYuMjc3YS45OTguOTk4IDAgMCAwIDEuNDE0LjAzMiAxIDEgMCAwIDAgLjAzMS0xLjQxNHpNMzYgNDEuMzE0YzAtNC44NSAzLjk0Ni04Ljc5NiA4Ljc5Ni04Ljc5NnM4Ljc5NiAzLjk0NiA4Ljc5NiA4Ljc5Ni0zLjk0NiA4Ljc5Ni04Ljc5NiA4Ljc5NlMzNiA0Ni4xNjQgMzYgNDEuMzE0ek0xMC40MzEgMTYuMDg4YzAgMy4wNyAyLjQ5OCA1LjU2OCA1LjU2OSA1LjU2OHM1LjU2OS0yLjQ5OCA1LjU2OS01LjU2OGMwLTMuMDcxLTIuNDk4LTUuNTY5LTUuNTY5LTUuNTY5cy01LjU2OSAyLjQ5OC01LjU2OSA1LjU2OXptOS4xMzggMGMwIDEuOTY4LTEuNjAyIDMuNTY4LTMuNTY5IDMuNTY4cy0zLjU2OS0xLjYwMS0zLjU2OS0zLjU2OCAxLjYwMi0zLjU2OSAzLjU2OS0zLjU2OSAzLjU2OSAxLjYwMSAzLjU2OSAzLjU2OXoiLz48cGF0aCBkPSJtMzAuODgyIDI4Ljk4NyA5LjE4LTEwLjA1NCAxMS4yNjIgMTAuMzIzYTEgMSAwIDAgMCAxLjM1MS0xLjQ3NWwtMTItMTFhMSAxIDAgMCAwLTEuNDE0LjA2M2wtOS43OTQgMTAuNzI3LTQuNzQzLTQuNzQzYTEuMDAzIDEuMDAzIDAgMCAwLTEuMzY4LS4wNDRMNi4zMzkgMzcuNzY4YTEgMSAwIDEgMCAxLjMyMiAxLjUwMWwxNi4zMTMtMTQuMzYyIDcuMzE5IDcuMzE4YS45OTkuOTk5IDAgMSAwIDEuNDE0LTEuNDE0bC0xLjgyNS0xLjgyNHoiLz48cGF0aCBkPSJNMzAgNDYuNTE4SDJ2LTQyaDU0djI4YTEgMSAwIDEgMCAyIDB2LTI5YTEgMSAwIDAgMC0xLTFIMWExIDEgMCAwIDAtMSAxdjQ0YTEgMSAwIDAgMCAxIDFoMjlhMSAxIDAgMSAwIDAtMnoiLz48L3N2Zz4=";
var ASSET_INFO_ICON_16PX = SVG_XML_PREFIX + "PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NjAgNDYwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA0NjAgNDYwIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBkPSJNMjMwIDBDMTAyLjk3NSAwIDAgMTAyLjk3NSAwIDIzMHMxMDIuOTc1IDIzMCAyMzAgMjMwIDIzMC0xMDIuOTc0IDIzMC0yMzBTMzU3LjAyNSAwIDIzMCAwem0zOC4zMzMgMzc3LjM2YzAgOC42NzYtNy4wMzQgMTUuNzEtMTUuNzEgMTUuNzFoLTQzLjEwMWMtOC42NzYgMC0xNS43MS03LjAzNC0xNS43MS0xNS43MVYyMDIuNDc3YzAtOC42NzYgNy4wMzMtMTUuNzEgMTUuNzEtMTUuNzFoNDMuMTAxYzguNjc2IDAgMTUuNzEgNy4wMzMgMTUuNzEgMTUuNzFWMzc3LjM2ek0yMzAgMTU3Yy0yMS41MzkgMC0zOS0xNy40NjEtMzktMzlzMTcuNDYxLTM5IDM5LTM5IDM5IDE3LjQ2MSAzOSAzOS0xNy40NjEgMzktMzkgMzl6Ii8+PC9zdmc+";
var ASSET_CLOSE_ICON_16PX = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAQgAAAEIBarqQRAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAE1SURBVDiNfdI7S0NBEAXgLya1otFgpbYSbISAgpXYi6CmiH9KCAiChaVga6OiWPgfRDQ+0itaGVNosXtluWwcuMzePfM4M3sq8lbHBubwg1dc4m1E/J/N4ghDPOIsfk/4xiEao5KX0McFljN4C9d4QTPXuY99jP3DsIoDPGM6BY5i5yI5R7O4q+ImFkJY2DCh3cAH2klyB+9J1xUMMAG7eCh1a+Mr+k48b5diXrFVwwLuS+BJ9MfR7+G0FHOHhTHhnXNWS87VDF4pcnfQK4Ep7XScNLmPTZgURNKKYENYWDpzW1BhscS1WHS8CDgURFJQrWcoF3c13KKbgg1BYQfy8xZWEzTTw1QZbAoKu8FqJnktdu5hcVSHmchiILzzuaDQvjBzV2m8yohCE1jHfPx/xhU+y4G/D75ELlRJsSYAAAAASUVORK5CYII="; //# sourceMappingURL=image-assets.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/storage.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PersistedDataManager": (()=>PersistedDataManager)
});
var PersistedDataFactory = function() {
    function PersistedDataFactory() {}
    PersistedDataFactory.createDefault = function() {
        return {
            hasPermission: false,
            lastUsedCameraId: null
        };
    };
    return PersistedDataFactory;
}();
var PersistedDataManager = function() {
    function PersistedDataManager() {
        this.data = PersistedDataFactory.createDefault();
        var data = localStorage.getItem(PersistedDataManager.LOCAL_STORAGE_KEY);
        if (!data) {
            this.reset();
        } else {
            this.data = JSON.parse(data);
        }
    }
    PersistedDataManager.prototype.hasCameraPermissions = function() {
        return this.data.hasPermission;
    };
    PersistedDataManager.prototype.getLastUsedCameraId = function() {
        return this.data.lastUsedCameraId;
    };
    PersistedDataManager.prototype.setHasPermission = function(hasPermission) {
        this.data.hasPermission = hasPermission;
        this.flush();
    };
    PersistedDataManager.prototype.setLastUsedCameraId = function(lastUsedCameraId) {
        this.data.lastUsedCameraId = lastUsedCameraId;
        this.flush();
    };
    PersistedDataManager.prototype.resetLastUsedCameraId = function() {
        this.data.lastUsedCameraId = null;
        this.flush();
    };
    PersistedDataManager.prototype.reset = function() {
        this.data = PersistedDataFactory.createDefault();
        this.flush();
    };
    PersistedDataManager.prototype.flush = function() {
        localStorage.setItem(PersistedDataManager.LOCAL_STORAGE_KEY, JSON.stringify(this.data));
    };
    PersistedDataManager.LOCAL_STORAGE_KEY = "HTML5_QRCODE_DATA";
    return PersistedDataManager;
}();
;
 //# sourceMappingURL=storage.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/ui.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LibraryInfoContainer": (()=>LibraryInfoContainer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$image$2d$assets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/image-assets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)");
;
;
var LibraryInfoDiv = function() {
    function LibraryInfoDiv() {
        this.infoDiv = document.createElement("div");
    }
    LibraryInfoDiv.prototype.renderInto = function(parent) {
        this.infoDiv.style.position = "absolute";
        this.infoDiv.style.top = "10px";
        this.infoDiv.style.right = "10px";
        this.infoDiv.style.zIndex = "2";
        this.infoDiv.style.display = "none";
        this.infoDiv.style.padding = "5pt";
        this.infoDiv.style.border = "1px solid #171717";
        this.infoDiv.style.fontSize = "10pt";
        this.infoDiv.style.background = "rgb(0 0 0 / 69%)";
        this.infoDiv.style.borderRadius = "5px";
        this.infoDiv.style.textAlign = "center";
        this.infoDiv.style.fontWeight = "400";
        this.infoDiv.style.color = "white";
        this.infoDiv.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LibraryInfoStrings"].poweredBy();
        var projectLink = document.createElement("a");
        projectLink.innerText = "ScanApp";
        projectLink.href = "https://scanapp.org";
        projectLink.target = "new";
        projectLink.style.color = "white";
        this.infoDiv.appendChild(projectLink);
        var breakElemFirst = document.createElement("br");
        var breakElemSecond = document.createElement("br");
        this.infoDiv.appendChild(breakElemFirst);
        this.infoDiv.appendChild(breakElemSecond);
        var reportIssueLink = document.createElement("a");
        reportIssueLink.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LibraryInfoStrings"].reportIssues();
        reportIssueLink.href = "https://github.com/mebjas/html5-qrcode/issues";
        reportIssueLink.target = "new";
        reportIssueLink.style.color = "white";
        this.infoDiv.appendChild(reportIssueLink);
        parent.appendChild(this.infoDiv);
    };
    LibraryInfoDiv.prototype.show = function() {
        this.infoDiv.style.display = "block";
    };
    LibraryInfoDiv.prototype.hide = function() {
        this.infoDiv.style.display = "none";
    };
    return LibraryInfoDiv;
}();
var LibraryInfoIcon = function() {
    function LibraryInfoIcon(onTapIn, onTapOut) {
        this.isShowingInfoIcon = true;
        this.onTapIn = onTapIn;
        this.onTapOut = onTapOut;
        this.infoIcon = document.createElement("img");
    }
    LibraryInfoIcon.prototype.renderInto = function(parent) {
        var _this = this;
        this.infoIcon.alt = "Info icon";
        this.infoIcon.src = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$image$2d$assets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSET_INFO_ICON_16PX"];
        this.infoIcon.style.position = "absolute";
        this.infoIcon.style.top = "4px";
        this.infoIcon.style.right = "4px";
        this.infoIcon.style.opacity = "0.6";
        this.infoIcon.style.cursor = "pointer";
        this.infoIcon.style.zIndex = "2";
        this.infoIcon.style.width = "16px";
        this.infoIcon.style.height = "16px";
        this.infoIcon.onmouseover = function(_) {
            return _this.onHoverIn();
        };
        this.infoIcon.onmouseout = function(_) {
            return _this.onHoverOut();
        };
        this.infoIcon.onclick = function(_) {
            return _this.onClick();
        };
        parent.appendChild(this.infoIcon);
    };
    LibraryInfoIcon.prototype.onHoverIn = function() {
        if (this.isShowingInfoIcon) {
            this.infoIcon.style.opacity = "1";
        }
    };
    LibraryInfoIcon.prototype.onHoverOut = function() {
        if (this.isShowingInfoIcon) {
            this.infoIcon.style.opacity = "0.6";
        }
    };
    LibraryInfoIcon.prototype.onClick = function() {
        if (this.isShowingInfoIcon) {
            this.isShowingInfoIcon = false;
            this.onTapIn();
            this.infoIcon.src = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$image$2d$assets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSET_CLOSE_ICON_16PX"];
            this.infoIcon.style.opacity = "1";
        } else {
            this.isShowingInfoIcon = true;
            this.onTapOut();
            this.infoIcon.src = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$image$2d$assets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSET_INFO_ICON_16PX"];
            this.infoIcon.style.opacity = "0.6";
        }
    };
    return LibraryInfoIcon;
}();
var LibraryInfoContainer = function() {
    function LibraryInfoContainer() {
        var _this = this;
        this.infoDiv = new LibraryInfoDiv();
        this.infoIcon = new LibraryInfoIcon(function() {
            _this.infoDiv.show();
        }, function() {
            _this.infoDiv.hide();
        });
    }
    LibraryInfoContainer.prototype.renderInto = function(parent) {
        this.infoDiv.renderInto(parent);
        this.infoIcon.renderInto(parent);
    };
    return LibraryInfoContainer;
}();
;
 //# sourceMappingURL=ui.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/camera/permissions.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CameraPermissions": (()=>CameraPermissions)
});
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
var CameraPermissions = function() {
    function CameraPermissions() {}
    CameraPermissions.hasPermissions = function() {
        return __awaiter(this, void 0, void 0, function() {
            var devices, _i, devices_1, device;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4,
                            navigator.mediaDevices.enumerateDevices()
                        ];
                    case 1:
                        devices = _a.sent();
                        for(_i = 0, devices_1 = devices; _i < devices_1.length; _i++){
                            device = devices_1[_i];
                            if (device.kind === "videoinput" && device.label) {
                                return [
                                    2,
                                    true
                                ];
                            }
                        }
                        return [
                            2,
                            false
                        ];
                }
            });
        });
    };
    return CameraPermissions;
}();
;
 //# sourceMappingURL=permissions.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/ui/scanner/scan-type-selector.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ScanTypeSelector": (()=>ScanTypeSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/core.js [app-client] (ecmascript)");
;
var ScanTypeSelector = function() {
    function ScanTypeSelector(supportedScanTypes) {
        this.supportedScanTypes = this.validateAndReturnScanTypes(supportedScanTypes);
    }
    ScanTypeSelector.prototype.getDefaultScanType = function() {
        return this.supportedScanTypes[0];
    };
    ScanTypeSelector.prototype.hasMoreThanOneScanType = function() {
        return this.supportedScanTypes.length > 1;
    };
    ScanTypeSelector.prototype.isCameraScanRequired = function() {
        for(var _i = 0, _a = this.supportedScanTypes; _i < _a.length; _i++){
            var scanType = _a[_i];
            if (ScanTypeSelector.isCameraScanType(scanType)) {
                return true;
            }
        }
        return false;
    };
    ScanTypeSelector.isCameraScanType = function(scanType) {
        return scanType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScanType"].SCAN_TYPE_CAMERA;
    };
    ScanTypeSelector.isFileScanType = function(scanType) {
        return scanType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScanType"].SCAN_TYPE_FILE;
    };
    ScanTypeSelector.prototype.validateAndReturnScanTypes = function(supportedScanTypes) {
        if (!supportedScanTypes || supportedScanTypes.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].DEFAULT_SUPPORTED_SCAN_TYPE;
        }
        var maxExpectedValues = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].DEFAULT_SUPPORTED_SCAN_TYPE.length;
        if (supportedScanTypes.length > maxExpectedValues) {
            throw "Max ".concat(maxExpectedValues, " values expected for ") + "supportedScanTypes";
        }
        for(var _i = 0, supportedScanTypes_1 = supportedScanTypes; _i < supportedScanTypes_1.length; _i++){
            var scanType = supportedScanTypes_1[_i];
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].DEFAULT_SUPPORTED_SCAN_TYPE.includes(scanType)) {
                throw "Unsupported scan type ".concat(scanType);
            }
        }
        return supportedScanTypes;
    };
    return ScanTypeSelector;
}();
;
 //# sourceMappingURL=scan-type-selector.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/ui/scanner/base.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseUiElementFactory": (()=>BaseUiElementFactory),
    "PublicUiElementIdAndClasses": (()=>PublicUiElementIdAndClasses)
});
var PublicUiElementIdAndClasses = function() {
    function PublicUiElementIdAndClasses() {}
    PublicUiElementIdAndClasses.ALL_ELEMENT_CLASS = "html5-qrcode-element";
    PublicUiElementIdAndClasses.CAMERA_PERMISSION_BUTTON_ID = "html5-qrcode-button-camera-permission";
    PublicUiElementIdAndClasses.CAMERA_START_BUTTON_ID = "html5-qrcode-button-camera-start";
    PublicUiElementIdAndClasses.CAMERA_STOP_BUTTON_ID = "html5-qrcode-button-camera-stop";
    PublicUiElementIdAndClasses.TORCH_BUTTON_ID = "html5-qrcode-button-torch";
    PublicUiElementIdAndClasses.CAMERA_SELECTION_SELECT_ID = "html5-qrcode-select-camera";
    PublicUiElementIdAndClasses.FILE_SELECTION_BUTTON_ID = "html5-qrcode-button-file-selection";
    PublicUiElementIdAndClasses.ZOOM_SLIDER_ID = "html5-qrcode-input-range-zoom";
    PublicUiElementIdAndClasses.SCAN_TYPE_CHANGE_ANCHOR_ID = "html5-qrcode-anchor-scan-type-change";
    PublicUiElementIdAndClasses.TORCH_BUTTON_CLASS_TORCH_ON = "html5-qrcode-button-torch-on";
    PublicUiElementIdAndClasses.TORCH_BUTTON_CLASS_TORCH_OFF = "html5-qrcode-button-torch-off";
    return PublicUiElementIdAndClasses;
}();
;
var BaseUiElementFactory = function() {
    function BaseUiElementFactory() {}
    BaseUiElementFactory.createElement = function(elementType, elementId) {
        var element = document.createElement(elementType);
        element.id = elementId;
        element.classList.add(PublicUiElementIdAndClasses.ALL_ELEMENT_CLASS);
        if (elementType === "button") {
            element.setAttribute("type", "button");
        }
        return element;
    };
    return BaseUiElementFactory;
}();
;
 //# sourceMappingURL=base.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/ui/scanner/torch-button.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TorchButton": (()=>TorchButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/base.js [app-client] (ecmascript)");
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
;
;
var TorchController = function() {
    function TorchController(torchCapability, buttonController, onTorchActionFailureCallback) {
        this.isTorchOn = false;
        this.torchCapability = torchCapability;
        this.buttonController = buttonController;
        this.onTorchActionFailureCallback = onTorchActionFailureCallback;
    }
    TorchController.prototype.isTorchEnabled = function() {
        return this.isTorchOn;
    };
    TorchController.prototype.flipState = function() {
        return __awaiter(this, void 0, void 0, function() {
            var isTorchOnExpected, error_1;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        this.buttonController.disable();
                        isTorchOnExpected = !this.isTorchOn;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([
                            1,
                            3,
                            ,
                            4
                        ]);
                        return [
                            4,
                            this.torchCapability.apply(isTorchOnExpected)
                        ];
                    case 2:
                        _a.sent();
                        this.updateUiBasedOnLatestSettings(this.torchCapability.value(), isTorchOnExpected);
                        return [
                            3,
                            4
                        ];
                    case 3:
                        error_1 = _a.sent();
                        this.propagateFailure(isTorchOnExpected, error_1);
                        this.buttonController.enable();
                        return [
                            3,
                            4
                        ];
                    case 4:
                        return [
                            2
                        ];
                }
            });
        });
    };
    TorchController.prototype.updateUiBasedOnLatestSettings = function(isTorchOn, isTorchOnExpected) {
        if (isTorchOn === isTorchOnExpected) {
            this.buttonController.setText(isTorchOnExpected ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].torchOffButton() : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].torchOnButton());
            this.isTorchOn = isTorchOnExpected;
        } else {
            this.propagateFailure(isTorchOnExpected);
        }
        this.buttonController.enable();
    };
    TorchController.prototype.propagateFailure = function(isTorchOnExpected, error) {
        var errorMessage = isTorchOnExpected ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].torchOnFailedMessage() : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].torchOffFailedMessage();
        if (error) {
            errorMessage += "; Error = " + error;
        }
        this.onTorchActionFailureCallback(errorMessage);
    };
    TorchController.prototype.reset = function() {
        this.isTorchOn = false;
    };
    return TorchController;
}();
var TorchButton = function() {
    function TorchButton(torchCapability, onTorchActionFailureCallback) {
        this.onTorchActionFailureCallback = onTorchActionFailureCallback;
        this.torchButton = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("button", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].TORCH_BUTTON_ID);
        this.torchController = new TorchController(torchCapability, this, onTorchActionFailureCallback);
    }
    TorchButton.prototype.render = function(parentElement, torchButtonOptions) {
        var _this = this;
        this.torchButton.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].torchOnButton();
        this.torchButton.style.display = torchButtonOptions.display;
        this.torchButton.style.marginLeft = torchButtonOptions.marginLeft;
        var $this = this;
        this.torchButton.addEventListener("click", function(_) {
            return __awaiter(_this, void 0, void 0, function() {
                return __generator(this, function(_a) {
                    switch(_a.label){
                        case 0:
                            return [
                                4,
                                $this.torchController.flipState()
                            ];
                        case 1:
                            _a.sent();
                            if ($this.torchController.isTorchEnabled()) {
                                $this.torchButton.classList.remove(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].TORCH_BUTTON_CLASS_TORCH_OFF);
                                $this.torchButton.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].TORCH_BUTTON_CLASS_TORCH_ON);
                            } else {
                                $this.torchButton.classList.remove(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].TORCH_BUTTON_CLASS_TORCH_ON);
                                $this.torchButton.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].TORCH_BUTTON_CLASS_TORCH_OFF);
                            }
                            return [
                                2
                            ];
                    }
                });
            });
        });
        parentElement.appendChild(this.torchButton);
    };
    TorchButton.prototype.updateTorchCapability = function(torchCapability) {
        this.torchController = new TorchController(torchCapability, this, this.onTorchActionFailureCallback);
    };
    TorchButton.prototype.getTorchButton = function() {
        return this.torchButton;
    };
    TorchButton.prototype.hide = function() {
        this.torchButton.style.display = "none";
    };
    TorchButton.prototype.show = function() {
        this.torchButton.style.display = "inline-block";
    };
    TorchButton.prototype.disable = function() {
        this.torchButton.disabled = true;
    };
    TorchButton.prototype.enable = function() {
        this.torchButton.disabled = false;
    };
    TorchButton.prototype.setText = function(text) {
        this.torchButton.innerText = text;
    };
    TorchButton.prototype.reset = function() {
        this.torchButton.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].torchOnButton();
        this.torchController.reset();
    };
    TorchButton.create = function(parentElement, torchCapability, torchButtonOptions, onTorchActionFailureCallback) {
        var button = new TorchButton(torchCapability, onTorchActionFailureCallback);
        button.render(parentElement, torchButtonOptions);
        return button;
    };
    return TorchButton;
}();
;
 //# sourceMappingURL=torch-button.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/ui/scanner/file-selection-ui.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileSelectionUi": (()=>FileSelectionUi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/base.js [app-client] (ecmascript)");
;
;
var FileSelectionUi = function() {
    function FileSelectionUi(parentElement, showOnRender, onFileSelected) {
        this.fileBasedScanRegion = this.createFileBasedScanRegion();
        this.fileBasedScanRegion.style.display = showOnRender ? "block" : "none";
        parentElement.appendChild(this.fileBasedScanRegion);
        var fileScanLabel = document.createElement("label");
        fileScanLabel.setAttribute("for", this.getFileScanInputId());
        fileScanLabel.style.display = "inline-block";
        this.fileBasedScanRegion.appendChild(fileScanLabel);
        this.fileSelectionButton = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("button", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].FILE_SELECTION_BUTTON_ID);
        this.setInitialValueToButton();
        this.fileSelectionButton.addEventListener("click", function(_) {
            fileScanLabel.click();
        });
        fileScanLabel.append(this.fileSelectionButton);
        this.fileScanInput = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("input", this.getFileScanInputId());
        this.fileScanInput.type = "file";
        this.fileScanInput.accept = "image/*";
        this.fileScanInput.style.display = "none";
        fileScanLabel.appendChild(this.fileScanInput);
        var $this = this;
        this.fileScanInput.addEventListener("change", function(e) {
            if (e == null || e.target == null) {
                return;
            }
            var target = e.target;
            if (target.files && target.files.length === 0) {
                return;
            }
            var fileList = target.files;
            var file = fileList[0];
            var fileName = file.name;
            $this.setImageNameToButton(fileName);
            onFileSelected(file);
        });
        var dragAndDropMessage = this.createDragAndDropMessage();
        this.fileBasedScanRegion.appendChild(dragAndDropMessage);
        this.fileBasedScanRegion.addEventListener("dragenter", function(event) {
            $this.fileBasedScanRegion.style.border = $this.fileBasedScanRegionActiveBorder();
            event.stopPropagation();
            event.preventDefault();
        });
        this.fileBasedScanRegion.addEventListener("dragleave", function(event) {
            $this.fileBasedScanRegion.style.border = $this.fileBasedScanRegionDefaultBorder();
            event.stopPropagation();
            event.preventDefault();
        });
        this.fileBasedScanRegion.addEventListener("dragover", function(event) {
            $this.fileBasedScanRegion.style.border = $this.fileBasedScanRegionActiveBorder();
            event.stopPropagation();
            event.preventDefault();
        });
        this.fileBasedScanRegion.addEventListener("drop", function(event) {
            event.stopPropagation();
            event.preventDefault();
            $this.fileBasedScanRegion.style.border = $this.fileBasedScanRegionDefaultBorder();
            var dataTransfer = event.dataTransfer;
            if (dataTransfer) {
                var files = dataTransfer.files;
                if (!files || files.length === 0) {
                    return;
                }
                var isAnyFileImage = false;
                for(var i = 0; i < files.length; ++i){
                    var file = files.item(i);
                    if (!file) {
                        continue;
                    }
                    var imageType = /image.*/;
                    if (!file.type.match(imageType)) {
                        continue;
                    }
                    isAnyFileImage = true;
                    var fileName = file.name;
                    $this.setImageNameToButton(fileName);
                    onFileSelected(file);
                    dragAndDropMessage.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].dragAndDropMessage();
                    break;
                }
                if (!isAnyFileImage) {
                    dragAndDropMessage.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].dragAndDropMessageOnlyImages();
                }
            }
        });
    }
    FileSelectionUi.prototype.hide = function() {
        this.fileBasedScanRegion.style.display = "none";
        this.fileScanInput.disabled = true;
    };
    FileSelectionUi.prototype.show = function() {
        this.fileBasedScanRegion.style.display = "block";
        this.fileScanInput.disabled = false;
    };
    FileSelectionUi.prototype.isShowing = function() {
        return this.fileBasedScanRegion.style.display === "block";
    };
    FileSelectionUi.prototype.resetValue = function() {
        this.fileScanInput.value = "";
        this.setInitialValueToButton();
    };
    FileSelectionUi.prototype.createFileBasedScanRegion = function() {
        var fileBasedScanRegion = document.createElement("div");
        fileBasedScanRegion.style.textAlign = "center";
        fileBasedScanRegion.style.margin = "auto";
        fileBasedScanRegion.style.width = "80%";
        fileBasedScanRegion.style.maxWidth = "600px";
        fileBasedScanRegion.style.border = this.fileBasedScanRegionDefaultBorder();
        fileBasedScanRegion.style.padding = "10px";
        fileBasedScanRegion.style.marginBottom = "10px";
        return fileBasedScanRegion;
    };
    FileSelectionUi.prototype.fileBasedScanRegionDefaultBorder = function() {
        return "6px dashed #ebebeb";
    };
    FileSelectionUi.prototype.fileBasedScanRegionActiveBorder = function() {
        return "6px dashed rgb(153 151 151)";
    };
    FileSelectionUi.prototype.createDragAndDropMessage = function() {
        var dragAndDropMessage = document.createElement("div");
        dragAndDropMessage.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].dragAndDropMessage();
        dragAndDropMessage.style.fontWeight = "400";
        return dragAndDropMessage;
    };
    FileSelectionUi.prototype.setImageNameToButton = function(imageFileName) {
        var MAX_CHARS = 20;
        if (imageFileName.length > MAX_CHARS) {
            var start8Chars = imageFileName.substring(0, 8);
            var length_1 = imageFileName.length;
            var last8Chars = imageFileName.substring(length_1 - 8, length_1);
            imageFileName = "".concat(start8Chars, "....").concat(last8Chars);
        }
        var newText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].fileSelectionChooseAnother() + " - " + imageFileName;
        this.fileSelectionButton.innerText = newText;
    };
    FileSelectionUi.prototype.setInitialValueToButton = function() {
        var initialText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].fileSelectionChooseImage() + " - " + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].fileSelectionNoImageSelected();
        this.fileSelectionButton.innerText = initialText;
    };
    FileSelectionUi.prototype.getFileScanInputId = function() {
        return "html5-qrcode-private-filescan-input";
    };
    FileSelectionUi.create = function(parentElement, showOnRender, onFileSelected) {
        var button = new FileSelectionUi(parentElement, showOnRender, onFileSelected);
        return button;
    };
    return FileSelectionUi;
}();
;
 //# sourceMappingURL=file-selection-ui.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/ui/scanner/camera-selection-ui.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CameraSelectionUi": (()=>CameraSelectionUi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)");
;
;
var CameraSelectionUi = function() {
    function CameraSelectionUi(cameras) {
        this.selectElement = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("select", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].CAMERA_SELECTION_SELECT_ID);
        this.cameras = cameras;
        this.options = [];
    }
    CameraSelectionUi.prototype.render = function(parentElement) {
        var cameraSelectionContainer = document.createElement("span");
        cameraSelectionContainer.style.marginRight = "10px";
        var numCameras = this.cameras.length;
        if (numCameras === 0) {
            throw new Error("No cameras found");
        }
        if (numCameras === 1) {
            cameraSelectionContainer.style.display = "none";
        } else {
            var selectCameraString = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].selectCamera();
            cameraSelectionContainer.innerText = "".concat(selectCameraString, " (").concat(this.cameras.length, ")  ");
        }
        var anonymousCameraId = 1;
        for(var _i = 0, _a = this.cameras; _i < _a.length; _i++){
            var camera = _a[_i];
            var value = camera.id;
            var name_1 = camera.label == null ? value : camera.label;
            if (!name_1 || name_1 === "") {
                name_1 = [
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].anonymousCameraPrefix(),
                    anonymousCameraId++
                ].join(" ");
            }
            var option = document.createElement("option");
            option.value = value;
            option.innerText = name_1;
            this.options.push(option);
            this.selectElement.appendChild(option);
        }
        cameraSelectionContainer.appendChild(this.selectElement);
        parentElement.appendChild(cameraSelectionContainer);
    };
    CameraSelectionUi.prototype.disable = function() {
        this.selectElement.disabled = true;
    };
    CameraSelectionUi.prototype.isDisabled = function() {
        return this.selectElement.disabled === true;
    };
    CameraSelectionUi.prototype.enable = function() {
        this.selectElement.disabled = false;
    };
    CameraSelectionUi.prototype.getValue = function() {
        return this.selectElement.value;
    };
    CameraSelectionUi.prototype.hasValue = function(value) {
        for(var _i = 0, _a = this.options; _i < _a.length; _i++){
            var option = _a[_i];
            if (option.value === value) {
                return true;
            }
        }
        return false;
    };
    CameraSelectionUi.prototype.setValue = function(value) {
        if (!this.hasValue(value)) {
            throw new Error("".concat(value, " is not present in the camera list."));
        }
        this.selectElement.value = value;
    };
    CameraSelectionUi.prototype.hasSingleItem = function() {
        return this.cameras.length === 1;
    };
    CameraSelectionUi.prototype.numCameras = function() {
        return this.cameras.length;
    };
    CameraSelectionUi.create = function(parentElement, cameras) {
        var cameraSelectUi = new CameraSelectionUi(cameras);
        cameraSelectUi.render(parentElement);
        return cameraSelectUi;
    };
    return CameraSelectionUi;
}();
;
 //# sourceMappingURL=camera-selection-ui.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/ui/scanner/camera-zoom-ui.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CameraZoomUi": (()=>CameraZoomUi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)");
;
;
var CameraZoomUi = function() {
    function CameraZoomUi() {
        this.onChangeCallback = null;
        this.zoomElementContainer = document.createElement("div");
        this.rangeInput = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("input", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].ZOOM_SLIDER_ID);
        this.rangeInput.type = "range";
        this.rangeText = document.createElement("span");
        this.rangeInput.min = "1";
        this.rangeInput.max = "5";
        this.rangeInput.value = "1";
        this.rangeInput.step = "0.1";
    }
    CameraZoomUi.prototype.render = function(parentElement, renderOnCreate) {
        this.zoomElementContainer.style.display = renderOnCreate ? "block" : "none";
        this.zoomElementContainer.style.padding = "5px 10px";
        this.zoomElementContainer.style.textAlign = "center";
        parentElement.appendChild(this.zoomElementContainer);
        this.rangeInput.style.display = "inline-block";
        this.rangeInput.style.width = "50%";
        this.rangeInput.style.height = "5px";
        this.rangeInput.style.background = "#d3d3d3";
        this.rangeInput.style.outline = "none";
        this.rangeInput.style.opacity = "0.7";
        var zoomString = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].zoom();
        this.rangeText.innerText = "".concat(this.rangeInput.value, "x ").concat(zoomString);
        this.rangeText.style.marginRight = "10px";
        var $this = this;
        this.rangeInput.addEventListener("input", function() {
            return $this.onValueChange();
        });
        this.rangeInput.addEventListener("change", function() {
            return $this.onValueChange();
        });
        this.zoomElementContainer.appendChild(this.rangeInput);
        this.zoomElementContainer.appendChild(this.rangeText);
    };
    CameraZoomUi.prototype.onValueChange = function() {
        var zoomString = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].zoom();
        this.rangeText.innerText = "".concat(this.rangeInput.value, "x ").concat(zoomString);
        if (this.onChangeCallback) {
            this.onChangeCallback(parseFloat(this.rangeInput.value));
        }
    };
    CameraZoomUi.prototype.setValues = function(minValue, maxValue, defaultValue, step) {
        this.rangeInput.min = minValue.toString();
        this.rangeInput.max = maxValue.toString();
        this.rangeInput.step = step.toString();
        this.rangeInput.value = defaultValue.toString();
        this.onValueChange();
    };
    CameraZoomUi.prototype.show = function() {
        this.zoomElementContainer.style.display = "block";
    };
    CameraZoomUi.prototype.hide = function() {
        this.zoomElementContainer.style.display = "none";
    };
    CameraZoomUi.prototype.setOnCameraZoomValueChangeCallback = function(onChangeCallback) {
        this.onChangeCallback = onChangeCallback;
    };
    CameraZoomUi.prototype.removeOnCameraZoomValueChangeCallback = function() {
        this.onChangeCallback = null;
    };
    CameraZoomUi.create = function(parentElement, renderOnCreate) {
        var cameraZoomUi = new CameraZoomUi();
        cameraZoomUi.render(parentElement, renderOnCreate);
        return cameraZoomUi;
    };
    return CameraZoomUi;
}();
;
 //# sourceMappingURL=camera-zoom-ui.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/html5-qrcode-scanner.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html5QrcodeScanner": (()=>Html5QrcodeScanner)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$html5$2d$qrcode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/html5-qrcode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/strings.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$image$2d$assets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/image-assets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$storage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/storage.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$permissions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/camera/permissions.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/scan-type-selector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$torch$2d$button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/torch-button.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$file$2d$selection$2d$ui$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/file-selection-ui.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$camera$2d$selection$2d$ui$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/camera-selection-ui.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$camera$2d$zoom$2d$ui$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/ui/scanner/camera-zoom-ui.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
var Html5QrcodeScannerStatus;
(function(Html5QrcodeScannerStatus) {
    Html5QrcodeScannerStatus[Html5QrcodeScannerStatus["STATUS_DEFAULT"] = 0] = "STATUS_DEFAULT";
    Html5QrcodeScannerStatus[Html5QrcodeScannerStatus["STATUS_SUCCESS"] = 1] = "STATUS_SUCCESS";
    Html5QrcodeScannerStatus[Html5QrcodeScannerStatus["STATUS_WARNING"] = 2] = "STATUS_WARNING";
    Html5QrcodeScannerStatus[Html5QrcodeScannerStatus["STATUS_REQUESTING_PERMISSION"] = 3] = "STATUS_REQUESTING_PERMISSION";
})(Html5QrcodeScannerStatus || (Html5QrcodeScannerStatus = {}));
function toHtml5QrcodeCameraScanConfig(config) {
    return {
        fps: config.fps,
        qrbox: config.qrbox,
        aspectRatio: config.aspectRatio,
        disableFlip: config.disableFlip,
        videoConstraints: config.videoConstraints
    };
}
function toHtml5QrcodeFullConfig(config, verbose) {
    return {
        formatsToSupport: config.formatsToSupport,
        useBarCodeDetectorIfSupported: config.useBarCodeDetectorIfSupported,
        experimentalFeatures: config.experimentalFeatures,
        verbose: verbose
    };
}
var Html5QrcodeScanner = function() {
    function Html5QrcodeScanner(elementId, config, verbose) {
        this.lastMatchFound = null;
        this.cameraScanImage = null;
        this.fileScanImage = null;
        this.fileSelectionUi = null;
        this.elementId = elementId;
        this.config = this.createConfig(config);
        this.verbose = verbose === true;
        if (!document.getElementById(elementId)) {
            throw "HTML Element with id=".concat(elementId, " not found");
        }
        this.scanTypeSelector = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScanTypeSelector"](this.config.supportedScanTypes);
        this.currentScanType = this.scanTypeSelector.getDefaultScanType();
        this.sectionSwapAllowed = true;
        this.logger = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseLoggger"](this.verbose);
        this.persistedDataManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$storage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PersistedDataManager"]();
        if (config.rememberLastUsedCamera !== true) {
            this.persistedDataManager.reset();
        }
    }
    Html5QrcodeScanner.prototype.render = function(qrCodeSuccessCallback, qrCodeErrorCallback) {
        var _this = this;
        this.lastMatchFound = null;
        this.qrCodeSuccessCallback = function(decodedText, result) {
            if (qrCodeSuccessCallback) {
                qrCodeSuccessCallback(decodedText, result);
            } else {
                if (_this.lastMatchFound === decodedText) {
                    return;
                }
                _this.lastMatchFound = decodedText;
                _this.setHeaderMessage(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].lastMatch(decodedText), Html5QrcodeScannerStatus.STATUS_SUCCESS);
            }
        };
        this.qrCodeErrorCallback = function(errorMessage, error) {
            if (qrCodeErrorCallback) {
                qrCodeErrorCallback(errorMessage, error);
            }
        };
        var container = document.getElementById(this.elementId);
        if (!container) {
            throw "HTML Element with id=".concat(this.elementId, " not found");
        }
        container.innerHTML = "";
        this.createBasicLayout(container);
        this.html5Qrcode = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$html5$2d$qrcode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5Qrcode"](this.getScanRegionId(), toHtml5QrcodeFullConfig(this.config, this.verbose));
    };
    Html5QrcodeScanner.prototype.pause = function(shouldPauseVideo) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNullOrUndefined"])(shouldPauseVideo) || shouldPauseVideo !== true) {
            shouldPauseVideo = false;
        }
        this.getHtml5QrcodeOrFail().pause(shouldPauseVideo);
    };
    Html5QrcodeScanner.prototype.resume = function() {
        this.getHtml5QrcodeOrFail().resume();
    };
    Html5QrcodeScanner.prototype.getState = function() {
        return this.getHtml5QrcodeOrFail().getState();
    };
    Html5QrcodeScanner.prototype.clear = function() {
        var _this = this;
        var emptyHtmlContainer = function() {
            var mainContainer = document.getElementById(_this.elementId);
            if (mainContainer) {
                mainContainer.innerHTML = "";
                _this.resetBasicLayout(mainContainer);
            }
        };
        if (this.html5Qrcode) {
            return new Promise(function(resolve, reject) {
                if (!_this.html5Qrcode) {
                    resolve();
                    return;
                }
                if (_this.html5Qrcode.isScanning) {
                    _this.html5Qrcode.stop().then(function(_) {
                        if (!_this.html5Qrcode) {
                            resolve();
                            return;
                        }
                        _this.html5Qrcode.clear();
                        emptyHtmlContainer();
                        resolve();
                    }).catch(function(error) {
                        if (_this.verbose) {
                            _this.logger.logError("Unable to stop qrcode scanner", error);
                        }
                        reject(error);
                    });
                } else {
                    _this.html5Qrcode.clear();
                    emptyHtmlContainer();
                    resolve();
                }
            });
        }
        return Promise.resolve();
    };
    Html5QrcodeScanner.prototype.getRunningTrackCapabilities = function() {
        return this.getHtml5QrcodeOrFail().getRunningTrackCapabilities();
    };
    Html5QrcodeScanner.prototype.getRunningTrackSettings = function() {
        return this.getHtml5QrcodeOrFail().getRunningTrackSettings();
    };
    Html5QrcodeScanner.prototype.applyVideoConstraints = function(videoConstaints) {
        return this.getHtml5QrcodeOrFail().applyVideoConstraints(videoConstaints);
    };
    Html5QrcodeScanner.prototype.getHtml5QrcodeOrFail = function() {
        if (!this.html5Qrcode) {
            throw "Code scanner not initialized.";
        }
        return this.html5Qrcode;
    };
    Html5QrcodeScanner.prototype.createConfig = function(config) {
        if (config) {
            if (!config.fps) {
                config.fps = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].SCAN_DEFAULT_FPS;
            }
            if (config.rememberLastUsedCamera !== !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].DEFAULT_REMEMBER_LAST_CAMERA_USED) {
                config.rememberLastUsedCamera = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].DEFAULT_REMEMBER_LAST_CAMERA_USED;
            }
            if (!config.supportedScanTypes) {
                config.supportedScanTypes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].DEFAULT_SUPPORTED_SCAN_TYPE;
            }
            return config;
        }
        return {
            fps: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].SCAN_DEFAULT_FPS,
            rememberLastUsedCamera: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].DEFAULT_REMEMBER_LAST_CAMERA_USED,
            supportedScanTypes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeConstants"].DEFAULT_SUPPORTED_SCAN_TYPE
        };
    };
    Html5QrcodeScanner.prototype.createBasicLayout = function(parent) {
        parent.style.position = "relative";
        parent.style.padding = "0px";
        parent.style.border = "1px solid silver";
        this.createHeader(parent);
        var qrCodeScanRegion = document.createElement("div");
        var scanRegionId = this.getScanRegionId();
        qrCodeScanRegion.id = scanRegionId;
        qrCodeScanRegion.style.width = "100%";
        qrCodeScanRegion.style.minHeight = "100px";
        qrCodeScanRegion.style.textAlign = "center";
        parent.appendChild(qrCodeScanRegion);
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScanTypeSelector"].isCameraScanType(this.currentScanType)) {
            this.insertCameraScanImageToScanRegion();
        } else {
            this.insertFileScanImageToScanRegion();
        }
        var qrCodeDashboard = document.createElement("div");
        var dashboardId = this.getDashboardId();
        qrCodeDashboard.id = dashboardId;
        qrCodeDashboard.style.width = "100%";
        parent.appendChild(qrCodeDashboard);
        this.setupInitialDashboard(qrCodeDashboard);
    };
    Html5QrcodeScanner.prototype.resetBasicLayout = function(mainContainer) {
        mainContainer.style.border = "none";
    };
    Html5QrcodeScanner.prototype.setupInitialDashboard = function(dashboard) {
        this.createSection(dashboard);
        this.createSectionControlPanel();
        if (this.scanTypeSelector.hasMoreThanOneScanType()) {
            this.createSectionSwap();
        }
    };
    Html5QrcodeScanner.prototype.createHeader = function(dashboard) {
        var header = document.createElement("div");
        header.style.textAlign = "left";
        header.style.margin = "0px";
        dashboard.appendChild(header);
        var libraryInfo = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LibraryInfoContainer"]();
        libraryInfo.renderInto(header);
        var headerMessageContainer = document.createElement("div");
        headerMessageContainer.id = this.getHeaderMessageContainerId();
        headerMessageContainer.style.display = "none";
        headerMessageContainer.style.textAlign = "center";
        headerMessageContainer.style.fontSize = "14px";
        headerMessageContainer.style.padding = "2px 10px";
        headerMessageContainer.style.margin = "4px";
        headerMessageContainer.style.borderTop = "1px solid #f6f6f6";
        header.appendChild(headerMessageContainer);
    };
    Html5QrcodeScanner.prototype.createSection = function(dashboard) {
        var section = document.createElement("div");
        section.id = this.getDashboardSectionId();
        section.style.width = "100%";
        section.style.padding = "10px 0px 10px 0px";
        section.style.textAlign = "left";
        dashboard.appendChild(section);
    };
    Html5QrcodeScanner.prototype.createCameraListUi = function(scpCameraScanRegion, requestPermissionContainer, requestPermissionButton) {
        var $this = this;
        $this.showHideScanTypeSwapLink(false);
        $this.setHeaderMessage(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].cameraPermissionRequesting());
        var createPermissionButtonIfNotExists = function() {
            if (!requestPermissionButton) {
                $this.createPermissionButton(scpCameraScanRegion, requestPermissionContainer);
            }
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$html5$2d$qrcode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5Qrcode"].getCameras().then(function(cameras) {
            $this.persistedDataManager.setHasPermission(true);
            $this.showHideScanTypeSwapLink(true);
            $this.resetHeaderMessage();
            if (cameras && cameras.length > 0) {
                scpCameraScanRegion.removeChild(requestPermissionContainer);
                $this.renderCameraSelection(cameras);
            } else {
                $this.setHeaderMessage(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].noCameraFound(), Html5QrcodeScannerStatus.STATUS_WARNING);
                createPermissionButtonIfNotExists();
            }
        }).catch(function(error) {
            $this.persistedDataManager.setHasPermission(false);
            if (requestPermissionButton) {
                requestPermissionButton.disabled = false;
            } else {
                createPermissionButtonIfNotExists();
            }
            $this.setHeaderMessage(error, Html5QrcodeScannerStatus.STATUS_WARNING);
            $this.showHideScanTypeSwapLink(true);
        });
    };
    Html5QrcodeScanner.prototype.createPermissionButton = function(scpCameraScanRegion, requestPermissionContainer) {
        var $this = this;
        var requestPermissionButton = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("button", this.getCameraPermissionButtonId());
        requestPermissionButton.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].cameraPermissionTitle();
        requestPermissionButton.addEventListener("click", function() {
            requestPermissionButton.disabled = true;
            $this.createCameraListUi(scpCameraScanRegion, requestPermissionContainer, requestPermissionButton);
        });
        requestPermissionContainer.appendChild(requestPermissionButton);
    };
    Html5QrcodeScanner.prototype.createPermissionsUi = function(scpCameraScanRegion, requestPermissionContainer) {
        var $this = this;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScanTypeSelector"].isCameraScanType(this.currentScanType) && this.persistedDataManager.hasCameraPermissions()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$permissions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CameraPermissions"].hasPermissions().then(function(hasPermissions) {
                if (hasPermissions) {
                    $this.createCameraListUi(scpCameraScanRegion, requestPermissionContainer);
                } else {
                    $this.persistedDataManager.setHasPermission(false);
                    $this.createPermissionButton(scpCameraScanRegion, requestPermissionContainer);
                }
            }).catch(function(_) {
                $this.persistedDataManager.setHasPermission(false);
                $this.createPermissionButton(scpCameraScanRegion, requestPermissionContainer);
            });
            return;
        }
        this.createPermissionButton(scpCameraScanRegion, requestPermissionContainer);
    };
    Html5QrcodeScanner.prototype.createSectionControlPanel = function() {
        var section = document.getElementById(this.getDashboardSectionId());
        var sectionControlPanel = document.createElement("div");
        section.appendChild(sectionControlPanel);
        var scpCameraScanRegion = document.createElement("div");
        scpCameraScanRegion.id = this.getDashboardSectionCameraScanRegionId();
        scpCameraScanRegion.style.display = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScanTypeSelector"].isCameraScanType(this.currentScanType) ? "block" : "none";
        sectionControlPanel.appendChild(scpCameraScanRegion);
        var requestPermissionContainer = document.createElement("div");
        requestPermissionContainer.style.textAlign = "center";
        scpCameraScanRegion.appendChild(requestPermissionContainer);
        if (this.scanTypeSelector.isCameraScanRequired()) {
            this.createPermissionsUi(scpCameraScanRegion, requestPermissionContainer);
        }
        this.renderFileScanUi(sectionControlPanel);
    };
    Html5QrcodeScanner.prototype.renderFileScanUi = function(parent) {
        var showOnRender = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScanTypeSelector"].isFileScanType(this.currentScanType);
        var $this = this;
        var onFileSelected = function(file) {
            if (!$this.html5Qrcode) {
                throw "html5Qrcode not defined";
            }
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScanTypeSelector"].isFileScanType($this.currentScanType)) {
                return;
            }
            $this.setHeaderMessage(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].loadingImage());
            $this.html5Qrcode.scanFileV2(file, true).then(function(html5qrcodeResult) {
                $this.resetHeaderMessage();
                $this.qrCodeSuccessCallback(html5qrcodeResult.decodedText, html5qrcodeResult);
            }).catch(function(error) {
                $this.setHeaderMessage(error, Html5QrcodeScannerStatus.STATUS_WARNING);
                $this.qrCodeErrorCallback(error, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeErrorFactory"].createFrom(error));
            });
        };
        this.fileSelectionUi = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$file$2d$selection$2d$ui$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileSelectionUi"].create(parent, showOnRender, onFileSelected);
    };
    Html5QrcodeScanner.prototype.renderCameraSelection = function(cameras) {
        var _this = this;
        var $this = this;
        var scpCameraScanRegion = document.getElementById(this.getDashboardSectionCameraScanRegionId());
        scpCameraScanRegion.style.textAlign = "center";
        var cameraZoomUi = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$camera$2d$zoom$2d$ui$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CameraZoomUi"].create(scpCameraScanRegion, false);
        var renderCameraZoomUiIfSupported = function(cameraCapabilities) {
            var zoomCapability = cameraCapabilities.zoomFeature();
            if (!zoomCapability.isSupported()) {
                return;
            }
            cameraZoomUi.setOnCameraZoomValueChangeCallback(function(zoomValue) {
                zoomCapability.apply(zoomValue);
            });
            var defaultZoom = 1;
            if (_this.config.defaultZoomValueIfSupported) {
                defaultZoom = _this.config.defaultZoomValueIfSupported;
            }
            defaultZoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clip"])(defaultZoom, zoomCapability.min(), zoomCapability.max());
            cameraZoomUi.setValues(zoomCapability.min(), zoomCapability.max(), defaultZoom, zoomCapability.step());
            cameraZoomUi.show();
        };
        var cameraSelectUi = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$camera$2d$selection$2d$ui$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CameraSelectionUi"].create(scpCameraScanRegion, cameras);
        var cameraActionContainer = document.createElement("span");
        var cameraActionStartButton = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("button", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].CAMERA_START_BUTTON_ID);
        cameraActionStartButton.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].scanButtonStartScanningText();
        cameraActionContainer.appendChild(cameraActionStartButton);
        var cameraActionStopButton = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("button", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].CAMERA_STOP_BUTTON_ID);
        cameraActionStopButton.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].scanButtonStopScanningText();
        cameraActionStopButton.style.display = "none";
        cameraActionStopButton.disabled = true;
        cameraActionContainer.appendChild(cameraActionStopButton);
        var torchButton;
        var createAndShowTorchButtonIfSupported = function(cameraCapabilities) {
            if (!cameraCapabilities.torchFeature().isSupported()) {
                if (torchButton) {
                    torchButton.hide();
                }
                return;
            }
            if (!torchButton) {
                torchButton = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$torch$2d$button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TorchButton"].create(cameraActionContainer, cameraCapabilities.torchFeature(), {
                    display: "none",
                    marginLeft: "5px"
                }, function(errorMessage) {
                    $this.setHeaderMessage(errorMessage, Html5QrcodeScannerStatus.STATUS_WARNING);
                });
            } else {
                torchButton.updateTorchCapability(cameraCapabilities.torchFeature());
            }
            torchButton.show();
        };
        scpCameraScanRegion.appendChild(cameraActionContainer);
        var resetCameraActionStartButton = function(shouldShow) {
            if (!shouldShow) {
                cameraActionStartButton.style.display = "none";
            }
            cameraActionStartButton.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].scanButtonStartScanningText();
            cameraActionStartButton.style.opacity = "1";
            cameraActionStartButton.disabled = false;
            if (shouldShow) {
                cameraActionStartButton.style.display = "inline-block";
            }
        };
        cameraActionStartButton.addEventListener("click", function(_) {
            cameraActionStartButton.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].scanButtonScanningStarting();
            cameraSelectUi.disable();
            cameraActionStartButton.disabled = true;
            cameraActionStartButton.style.opacity = "0.5";
            if (_this.scanTypeSelector.hasMoreThanOneScanType()) {
                $this.showHideScanTypeSwapLink(false);
            }
            $this.resetHeaderMessage();
            var cameraId = cameraSelectUi.getValue();
            $this.persistedDataManager.setLastUsedCameraId(cameraId);
            $this.html5Qrcode.start(cameraId, toHtml5QrcodeCameraScanConfig($this.config), $this.qrCodeSuccessCallback, $this.qrCodeErrorCallback).then(function(_) {
                cameraActionStopButton.disabled = false;
                cameraActionStopButton.style.display = "inline-block";
                resetCameraActionStartButton(false);
                var cameraCapabilities = $this.html5Qrcode.getRunningTrackCameraCapabilities();
                if (_this.config.showTorchButtonIfSupported === true) {
                    createAndShowTorchButtonIfSupported(cameraCapabilities);
                }
                if (_this.config.showZoomSliderIfSupported === true) {
                    renderCameraZoomUiIfSupported(cameraCapabilities);
                }
            }).catch(function(error) {
                $this.showHideScanTypeSwapLink(true);
                cameraSelectUi.enable();
                resetCameraActionStartButton(true);
                $this.setHeaderMessage(error, Html5QrcodeScannerStatus.STATUS_WARNING);
            });
        });
        if (cameraSelectUi.hasSingleItem()) {
            cameraActionStartButton.click();
        }
        cameraActionStopButton.addEventListener("click", function(_) {
            if (!$this.html5Qrcode) {
                throw "html5Qrcode not defined";
            }
            cameraActionStopButton.disabled = true;
            $this.html5Qrcode.stop().then(function(_) {
                if (_this.scanTypeSelector.hasMoreThanOneScanType()) {
                    $this.showHideScanTypeSwapLink(true);
                }
                cameraSelectUi.enable();
                cameraActionStartButton.disabled = false;
                cameraActionStopButton.style.display = "none";
                cameraActionStartButton.style.display = "inline-block";
                if (torchButton) {
                    torchButton.reset();
                    torchButton.hide();
                }
                cameraZoomUi.removeOnCameraZoomValueChangeCallback();
                cameraZoomUi.hide();
                $this.insertCameraScanImageToScanRegion();
            }).catch(function(error) {
                cameraActionStopButton.disabled = false;
                $this.setHeaderMessage(error, Html5QrcodeScannerStatus.STATUS_WARNING);
            });
        });
        if ($this.persistedDataManager.getLastUsedCameraId()) {
            var cameraId = $this.persistedDataManager.getLastUsedCameraId();
            if (cameraSelectUi.hasValue(cameraId)) {
                cameraSelectUi.setValue(cameraId);
                cameraActionStartButton.click();
            } else {
                $this.persistedDataManager.resetLastUsedCameraId();
            }
        }
    };
    Html5QrcodeScanner.prototype.createSectionSwap = function() {
        var $this = this;
        var TEXT_IF_CAMERA_SCAN_SELECTED = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].textIfCameraScanSelected();
        var TEXT_IF_FILE_SCAN_SELECTED = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].textIfFileScanSelected();
        var section = document.getElementById(this.getDashboardSectionId());
        var switchContainer = document.createElement("div");
        switchContainer.style.textAlign = "center";
        var switchScanTypeLink = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseUiElementFactory"].createElement("span", this.getDashboardSectionSwapLinkId());
        switchScanTypeLink.style.textDecoration = "underline";
        switchScanTypeLink.style.cursor = "pointer";
        switchScanTypeLink.innerText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScanTypeSelector"].isCameraScanType(this.currentScanType) ? TEXT_IF_CAMERA_SCAN_SELECTED : TEXT_IF_FILE_SCAN_SELECTED;
        switchScanTypeLink.addEventListener("click", function() {
            if (!$this.sectionSwapAllowed) {
                if ($this.verbose) {
                    $this.logger.logError("Section swap called when not allowed");
                }
                return;
            }
            $this.resetHeaderMessage();
            $this.fileSelectionUi.resetValue();
            $this.sectionSwapAllowed = false;
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$scan$2d$type$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScanTypeSelector"].isCameraScanType($this.currentScanType)) {
                $this.clearScanRegion();
                $this.getCameraScanRegion().style.display = "none";
                $this.fileSelectionUi.show();
                switchScanTypeLink.innerText = TEXT_IF_FILE_SCAN_SELECTED;
                $this.currentScanType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScanType"].SCAN_TYPE_FILE;
                $this.insertFileScanImageToScanRegion();
            } else {
                $this.clearScanRegion();
                $this.getCameraScanRegion().style.display = "block";
                $this.fileSelectionUi.hide();
                switchScanTypeLink.innerText = TEXT_IF_CAMERA_SCAN_SELECTED;
                $this.currentScanType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScanType"].SCAN_TYPE_CAMERA;
                $this.insertCameraScanImageToScanRegion();
                $this.startCameraScanIfPermissionExistsOnSwap();
            }
            $this.sectionSwapAllowed = true;
        });
        switchContainer.appendChild(switchScanTypeLink);
        section.appendChild(switchContainer);
    };
    Html5QrcodeScanner.prototype.startCameraScanIfPermissionExistsOnSwap = function() {
        var _this = this;
        var $this = this;
        if (this.persistedDataManager.hasCameraPermissions()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$camera$2f$permissions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CameraPermissions"].hasPermissions().then(function(hasPermissions) {
                if (hasPermissions) {
                    var permissionButton = document.getElementById($this.getCameraPermissionButtonId());
                    if (!permissionButton) {
                        _this.logger.logError("Permission button not found, fail;");
                        throw "Permission button not found";
                    }
                    permissionButton.click();
                } else {
                    $this.persistedDataManager.setHasPermission(false);
                }
            }).catch(function(_) {
                $this.persistedDataManager.setHasPermission(false);
            });
            return;
        }
    };
    Html5QrcodeScanner.prototype.resetHeaderMessage = function() {
        var messageDiv = document.getElementById(this.getHeaderMessageContainerId());
        messageDiv.style.display = "none";
    };
    Html5QrcodeScanner.prototype.setHeaderMessage = function(messageText, scannerStatus) {
        if (!scannerStatus) {
            scannerStatus = Html5QrcodeScannerStatus.STATUS_DEFAULT;
        }
        var messageDiv = this.getHeaderMessageDiv();
        messageDiv.innerText = messageText;
        messageDiv.style.display = "block";
        switch(scannerStatus){
            case Html5QrcodeScannerStatus.STATUS_SUCCESS:
                messageDiv.style.background = "rgba(106, 175, 80, 0.26)";
                messageDiv.style.color = "#477735";
                break;
            case Html5QrcodeScannerStatus.STATUS_WARNING:
                messageDiv.style.background = "rgba(203, 36, 49, 0.14)";
                messageDiv.style.color = "#cb2431";
                break;
            case Html5QrcodeScannerStatus.STATUS_DEFAULT:
            default:
                messageDiv.style.background = "rgba(0, 0, 0, 0)";
                messageDiv.style.color = "rgb(17, 17, 17)";
                break;
        }
    };
    Html5QrcodeScanner.prototype.showHideScanTypeSwapLink = function(shouldDisplay) {
        if (this.scanTypeSelector.hasMoreThanOneScanType()) {
            if (shouldDisplay !== true) {
                shouldDisplay = false;
            }
            this.sectionSwapAllowed = shouldDisplay;
            this.getDashboardSectionSwapLink().style.display = shouldDisplay ? "inline-block" : "none";
        }
    };
    Html5QrcodeScanner.prototype.insertCameraScanImageToScanRegion = function() {
        var $this = this;
        var qrCodeScanRegion = document.getElementById(this.getScanRegionId());
        if (this.cameraScanImage) {
            qrCodeScanRegion.innerHTML = "<br>";
            qrCodeScanRegion.appendChild(this.cameraScanImage);
            return;
        }
        this.cameraScanImage = new Image;
        this.cameraScanImage.onload = function(_) {
            qrCodeScanRegion.innerHTML = "<br>";
            qrCodeScanRegion.appendChild($this.cameraScanImage);
        };
        this.cameraScanImage.width = 64;
        this.cameraScanImage.style.opacity = "0.8";
        this.cameraScanImage.src = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$image$2d$assets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSET_CAMERA_SCAN"];
        this.cameraScanImage.alt = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].cameraScanAltText();
    };
    Html5QrcodeScanner.prototype.insertFileScanImageToScanRegion = function() {
        var $this = this;
        var qrCodeScanRegion = document.getElementById(this.getScanRegionId());
        if (this.fileScanImage) {
            qrCodeScanRegion.innerHTML = "<br>";
            qrCodeScanRegion.appendChild(this.fileScanImage);
            return;
        }
        this.fileScanImage = new Image;
        this.fileScanImage.onload = function(_) {
            qrCodeScanRegion.innerHTML = "<br>";
            qrCodeScanRegion.appendChild($this.fileScanImage);
        };
        this.fileScanImage.width = 64;
        this.fileScanImage.style.opacity = "0.8";
        this.fileScanImage.src = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$image$2d$assets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSET_FILE_SCAN"];
        this.fileScanImage.alt = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$strings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Html5QrcodeScannerStrings"].fileScanAltText();
    };
    Html5QrcodeScanner.prototype.clearScanRegion = function() {
        var qrCodeScanRegion = document.getElementById(this.getScanRegionId());
        qrCodeScanRegion.innerHTML = "";
    };
    Html5QrcodeScanner.prototype.getDashboardSectionId = function() {
        return "".concat(this.elementId, "__dashboard_section");
    };
    Html5QrcodeScanner.prototype.getDashboardSectionCameraScanRegionId = function() {
        return "".concat(this.elementId, "__dashboard_section_csr");
    };
    Html5QrcodeScanner.prototype.getDashboardSectionSwapLinkId = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].SCAN_TYPE_CHANGE_ANCHOR_ID;
    };
    Html5QrcodeScanner.prototype.getScanRegionId = function() {
        return "".concat(this.elementId, "__scan_region");
    };
    Html5QrcodeScanner.prototype.getDashboardId = function() {
        return "".concat(this.elementId, "__dashboard");
    };
    Html5QrcodeScanner.prototype.getHeaderMessageContainerId = function() {
        return "".concat(this.elementId, "__header_message");
    };
    Html5QrcodeScanner.prototype.getCameraPermissionButtonId = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$ui$2f$scanner$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PublicUiElementIdAndClasses"].CAMERA_PERMISSION_BUTTON_ID;
    };
    Html5QrcodeScanner.prototype.getCameraScanRegion = function() {
        return document.getElementById(this.getDashboardSectionCameraScanRegionId());
    };
    Html5QrcodeScanner.prototype.getDashboardSectionSwapLink = function() {
        return document.getElementById(this.getDashboardSectionSwapLinkId());
    };
    Html5QrcodeScanner.prototype.getHeaderMessageDiv = function() {
        return document.getElementById(this.getHeaderMessageContainerId());
    };
    return Html5QrcodeScanner;
}();
;
 //# sourceMappingURL=html5-qrcode-scanner.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$html5$2d$qrcode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/html5-qrcode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$html5$2d$qrcode$2d$scanner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/html5-qrcode-scanner.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$state$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/state-manager.js [app-client] (ecmascript)");
;
;
;
;
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/html5-qrcode/esm/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$html5$2d$qrcode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/html5-qrcode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$html5$2d$qrcode$2d$scanner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/html5-qrcode-scanner.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$state$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/state-manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html5$2d$qrcode$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/html5-qrcode/esm/index.js [app-client] (ecmascript) <locals>");
}}),
}]);

//# sourceMappingURL=node_modules_html5-qrcode_esm_62b22b9a._.js.map