import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyPassword, generateAdminToken } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    // Demo mode authentication (when database is not available)
    const isDemoMode = !process.env.DATABASE_URL || process.env.DATABASE_URL.includes('username:password');

    if (isDemoMode) {
      // Demo credentials
      const demoEmail = '<EMAIL>';
      const demoPassword = 'admin123';

      if (email.toLowerCase() !== demoEmail || password !== demoPassword) {
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: 'Invalid credentials'
        }, { status: 401 });
      }

      // Create demo admin object
      const demoAdmin = {
        id: 'demo-admin-id',
        email: demoEmail,
        name: 'Demo Admin',
        role: 'admin'
      };

      // Generate JWT token
      const token = generateAdminToken({
        adminId: demoAdmin.id,
        email: demoAdmin.email,
        name: demoAdmin.name,
        role: demoAdmin.role
      });

      // Create response with token
      const response = NextResponse.json<ApiResponse<{
        admin: {
          id: string;
          email: string;
          name: string;
          role: string;
        };
        token: string;
      }>>({
        success: true,
        data: {
          admin: demoAdmin,
          token
        },
        message: 'Login successful (Demo Mode)'
      });

      // Set HTTP-only cookie for additional security
      response.cookies.set('admin-token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 // 24 hours
      });

      return response;
    }

    // Production mode with database
    try {
      // Find admin by email
      const admin = await prisma.admin.findUnique({
        where: { email: email.toLowerCase() }
      });

      if (!admin) {
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: 'Invalid credentials'
        }, { status: 401 });
      }

      // Check if admin is active
      if (!admin.isActive) {
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: 'Account is deactivated'
        }, { status: 401 });
      }

      // Verify password
      const isValidPassword = await verifyPassword(password, admin.password);
      if (!isValidPassword) {
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: 'Invalid credentials'
        }, { status: 401 });
      }

      // Update last login
      await prisma.admin.update({
        where: { id: admin.id },
        data: { lastLogin: new Date() }
      });
    } catch (dbError) {
      console.error('Database error, falling back to demo mode:', dbError);

      // Fallback to demo mode if database fails
      const demoEmail = '<EMAIL>';
      const demoPassword = 'admin123';

      if (email.toLowerCase() !== demoEmail || password !== demoPassword) {
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: 'Invalid credentials'
        }, { status: 401 });
      }

      const demoAdmin = {
        id: 'demo-admin-id',
        email: demoEmail,
        name: 'Demo Admin',
        role: 'admin'
      };

      const token = generateAdminToken({
        adminId: demoAdmin.id,
        email: demoAdmin.email,
        name: demoAdmin.name,
        role: demoAdmin.role
      });

      const response = NextResponse.json<ApiResponse<{
        admin: {
          id: string;
          email: string;
          name: string;
          role: string;
        };
        token: string;
      }>>({
        success: true,
        data: {
          admin: demoAdmin,
          token
        },
        message: 'Login successful (Demo Mode - Database Unavailable)'
      });

      response.cookies.set('admin-token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60
      });

      return response;
    }

    // This code only runs in production mode with database
    // Generate JWT token
    const token = generateAdminToken({
      adminId: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role
    });

    // Create response with token
    const response = NextResponse.json<ApiResponse<{
      admin: {
        id: string;
        email: string;
        name: string;
        role: string;
      };
      token: string;
    }>>({
      success: true,
      data: {
        admin: {
          id: admin.id,
          email: admin.email,
          name: admin.name,
          role: admin.role
        },
        token
      },
      message: 'Login successful'
    });

    // Set HTTP-only cookie for additional security
    response.cookies.set('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    });

    return response;

  } catch (error) {
    console.error('Admin login error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
