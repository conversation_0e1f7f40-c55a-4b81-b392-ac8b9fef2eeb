{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,MAAM,MAAM,UAAU,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;AAExD;;GAEG;AACH,MAAM,MAAM,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;AAE3D,OAAO,EAAE,OAAO,EAAE,cAAc,EAAmB,MAAM,WAAW,CAAA;AA2DpE;;;GAGG;AACH,MAAM,MAAM,UAAU,CACpB,CAAC,SAAS,UAAU,GAAG,UAAU,EACjC,CAAC,SAAS,OAAO,GAAG,OAAO,IAE3B;IAAC,CAAC;IAAE,CAAC;CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,GACxC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,GAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,OAAO,EAAE,GAC5C,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,GACzC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,GACzC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,OAAO,GAC3C,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,MAAM,GAAG,MAAM,EAAE,GACtD,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO,EAAE,GACzD,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,MAAM,GAAG,MAAM,EAAE,GACtD,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAC9D,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,GACnE,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,CAAA;AAE/D;;;GAGG;AACH,MAAM,MAAM,gBAAgB,CAC1B,CAAC,SAAS,UAAU,EACpB,CAAC,SAAS,OAAO,GAAG,OAAO,EAC3B,CAAC,SACG,SAAS,GACT,CAAC,CAAC,SAAS,SAAS,GAAG,KAAK,GAC1B,CAAC,SAAS,QAAQ,GAAG,SAAS,MAAM,EAAE,GACtC,CAAC,SAAS,QAAQ,GAAG,SAAS,MAAM,EAAE,GACtC,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,EAAE,CAAC,GACxC,SAAS,GACT,CAAC,CAAC,SAAS,SAAS,GAAG,KAAK,GAC1B,CAAC,SAAS,QAAQ,GAAG,SAAS,MAAM,EAAE,GACtC,CAAC,SAAS,QAAQ,GAAG,SAAS,MAAM,EAAE,GACtC,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,EAAE,CAAC,IAC1C;IACF,OAAO,CAAC,EACJ,SAAS,GACT,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GACf,CAAC,CAAC,SAAS,MAAM,EAAE,GAAG,MAAM,EAAE,GAC5B,CAAC,SAAS,KAAK,GACb,CAAC,CAAC,MAAM,CAAC,GACT,CAAC,CAAC,MAAM,CAAC,EAAE,GACb,OAAO,CAAC,CAAC,CAAA;IACjB,YAAY,CAAC,EAAE,CAAC,CAAA;IAChB,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,QAAQ,CAAC,EACL,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACvC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,CAAA;IAC7B,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC1B,IAAI,CAAC,EAAE,CAAC,CAAA;IACR,IAAI,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,KAAK,GAAG,MAAM,CAAA;IAC3C,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,GAAG,MAAM,GAAG,KAAK,CAAA;CACxC,GAAG,CAAC,CAAC,SAAS,KAAK,GAAG;IAAE,QAAQ,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;CAAE,GACrD,CAAC,SAAS,IAAI,GAAG;IAAE,QAAQ,EAAE,IAAI,CAAA;CAAE,GACnC;IAAE,QAAQ,CAAC,EAAE,OAAO,CAAA;CAAE,CAAC,CAAA;AAEzB;;;GAGG;AACH,MAAM,MAAM,aAAa,CACvB,CAAC,SAAS,UAAU,EACpB,CAAC,SAAS,OAAO,GAAG,OAAO,IACzB;IACF,CAAC,UAAU,EAAE,MAAM,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAC7C,CAAA;AAED;;GAEG;AACH,MAAM,MAAM,oBAAoB,CAC9B,CAAC,SAAS,UAAU,EACpB,CAAC,SAAS,OAAO,EACjB,CAAC,SAAS,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,IAC3B;KACD,UAAU,IAAI,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;CAChD,CAAA;AAED;;;;GAIG;AACH,MAAM,MAAM,SAAS,CAAC,CAAC,SAAS,OAAO,IACrC,CAAC,SAAS,IAAI,GACZ;IACE,QAAQ,EAAE,IAAI,CAAA;IACd,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,GACD,CAAC,SAAS,KAAK,GACf;IACE,QAAQ,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;IAC5B,KAAK,CAAC,EAAE,SAAS,CAAA;CAClB,GACD;IACE,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAC9B,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,CAAA;AAEL;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAC1B,CAAC,SAAS,UAAU,EACpB,CAAC,SAAS,OAAO,GAAG,OAAO,IACzB;IACF,IAAI,EAAE,CAAC,CAAA;IACP,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC1B,OAAO,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAA;IACtC,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,IAAI,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,CAAA;IAC3D,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAChD,YAAY,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,GAC5C,CAAC,SAAS,QAAQ,GAAG,SAAS,MAAM,EAAE,GACtC,CAAC,SAAS,QAAQ,GAAG,SAAS,MAAM,EAAE,GACtC,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,EAAE,CAAA;CACxC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;AAEhB,eAAO,MAAM,YAAY,MAAO,MAAM,oBAEiB,CAAA;AA8CvD,eAAO,MAAM,cAAc,+CACtB,GAAG,QACA,CAAC,SACA,CAAC,gCAcc,CAAA;AAExB;;;GAGG;AACH,MAAM,MAAM,SAAS,GAAG;IACtB,CAAC,UAAU,EAAE,MAAM,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAA;CACnD,CAAA;AAED;;GAEG;AACH,MAAM,MAAM,cAAc,CAAC,CAAC,SAAS,SAAS,IAAI;KAC/C,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAC5C,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,EAAE,CACtC,GACC,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,EAAE,KAAK,CAAC,GACvD,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,GAC5B,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,EAAE,IAAI,CAAC,GACxD,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,EAAE,GAC9B,KAAK,GACP,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,GACvD,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,GACxD,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,GACvD,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,GACxD,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,OAAO,GACzD,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,OAAO,EAAE,GAC1D,KAAK;CACR,CAAA;AAED;;GAEG;AACH,MAAM,MAAM,MAAM,CAAC,CAAC,SAAS,SAAS,IAAI;IACxC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,CAAA;IACzB,WAAW,EAAE,MAAM,EAAE,CAAA;CACtB,CAAA;AA0PD;;GAEG;AACH,MAAM,WAAW,GAAG;IAClB,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,IAAI,CAAC,EAAE,MAAM,CAAA;CACd;AAED;;;;;;;;GAQG;AACH,MAAM,WAAW,OAAQ,SAAQ,GAAG;IAClC,IAAI,EAAE,SAAS,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,CAAC,EAAE,EAAE,CAAA;IACT,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,KAAK,EAAE,MAAM,CAAA;IACb,GAAG,CAAC,EAAE,OAAO,CAAA;CACd;AAID;;;;;GAKG;AACH,MAAM,WAAW,WAAY,SAAQ,GAAG;IACtC,IAAI,EAAE,aAAa,CAAA;IACnB,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,CAAC,EAAE,EAAE,CAAA;IACT,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,GAAG,CAAC,EAAE,OAAO,CAAA;CACd;AAKD;;;GAGG;AACH,MAAM,MAAM,OAAO,GAAG,OAAO,GAAG,WAAW,CAAA;AAE3C;;GAEG;AACH,MAAM,MAAM,UAAU,GAClB,OAAO,GACP;IACE,IAAI,EAAE,QAAQ,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,gBAAgB,CAAC,UAAU,CAAC,CAAA;CACpC,CAAA;AAEL;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAA;IAE1B;;;;OAIG;IACH,SAAS,CAAC,EAAE,MAAM,CAAA;IAElB;;;OAGG;IACH,GAAG,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;KAAE,CAAA;IAEzC;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IAEd;;;;;;;;;;;OAWG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAA;IAE1B;;;;OAIG;IACH,oBAAoB,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAA;CAChD;AAED;;;GAGG;AACH,qBAAa,IAAI,CAAC,CAAC,SAAS,SAAS,GAAG,EAAE;;gBAW5B,OAAO,GAAE,WAAgB;IAarC;;;;;OAKG;IACH,eAAe,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,MAAM,SAAK;IA6BtD;;;;;;;;;;OAUG;IACH,KAAK,CAAC,IAAI,GAAE,MAAM,EAAiB,GAAG,MAAM,CAAC,CAAC,CAAC;IAQ/C,eAAe;IAYf,aAAa,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IAS1B;;;;;OAKG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;IAmKnC;;;OAGG;IACH,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IA4DtD,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IAWrB;;OAEG;IACH,OAAO,CACL,IAAI,EAAE,MAAM,EACZ,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAC7B,EAAE,GAAW,EAAE,GAAE;QAAE,GAAG,CAAC,EAAE,OAAO,CAAA;KAAO,GACtC,IAAI,CAAC,CAAC,CAAC;IAQV;;OAEG;IACH,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,GAAE;QAAE,GAAG,CAAC,EAAE,OAAO,CAAA;KAAO,GAAG,IAAI,CAAC,CAAC,CAAC;IAKnE;;OAEG;IACH,GAAG,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,EAC1C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAIrD;;OAEG;IACH,OAAO,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,CAAC,EACvC,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAIpD;;OAEG;IACH,GAAG,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,EAC1C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAIrD;;OAEG;IACH,OAAO,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,CAAC,EACvC,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAIpD;;OAEG;IACH,IAAI,CAAC,CAAC,SAAS,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,EAC5C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAItD;;OAEG;IACH,QAAQ,CAAC,CAAC,SAAS,aAAa,CAAC,SAAS,CAAC,EACzC,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAIrD;;;;OAIG;IACH,SAAS,CAAC,CAAC,SAAS,SAAS,EAAE,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IA4EtD;;OAEG;IACH,KAAK,IAAI,MAAM;IAgGf;;OAEG;IACH,aAAa,IAAI,MAAM;IAgIvB;;OAEG;IACH,MAAM;;;;;;;;;;;;;IAqBN;;OAEG;IACH,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc;CAGpD;AAsED;;GAEG;AACH,eAAO,MAAM,IAAI,aAAa,WAAW,aAA2B,CAAA"}