import {
  isValidEmail,
  isValid<PERSON>ame,
  isValidDate,
  isValidZodiacSign,
  isValidLanguage,
  isValidHoroscopeType,
  isValidContent,
  isValidUUID,
  sanitizeString,
  validatePagination,
  isValidSearchQuery,
  validateRequestBody
} from '../validation';

describe('Validation Utilities', () => {
  describe('isValidEmail', () => {
    test('should validate correct email formats', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(true);
      });
    });

    test('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        'user@.com',
        '<EMAIL>',
        'user@example',
        '',
        'a'.repeat(250) + '@example.com' // too long
      ];

      invalidEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(false);
      });
    });
  });

  describe('isValidName', () => {
    test('should validate correct name formats', () => {
      const validNames = [
        'John Doe',
        'Mary Jane',
        "O'Connor",
        'Jean-Pierre',
        'Anna Maria',
        '李小明' // Unicode characters should be handled
      ];

      validNames.forEach(name => {
        expect(isValidName(name)).toBe(true);
      });
    });

    test('should reject invalid name formats', () => {
      const invalidNames = [
        '',
        'A', // too short
        'A'.repeat(101), // too long
        'John123', // contains numbers
        'John@Doe', // contains special characters
        '   ', // only whitespace
        null,
        undefined
      ];

      invalidNames.forEach(name => {
        expect(isValidName(name as string)).toBe(false);
      });
    });
  });

  describe('isValidDate', () => {
    test('should validate correct date formats', () => {
      const validDates = [
        '1990-01-01',
        '2000-12-31',
        '1985-06-15',
        new Date().toISOString().split('T')[0] // today
      ];

      validDates.forEach(date => {
        expect(isValidDate(date)).toBe(true);
      });
    });

    test('should reject invalid dates', () => {
      const invalidDates = [
        'invalid-date',
        '2025-01-01', // future date
        '1800-01-01', // too far in past
        '2000-13-01', // invalid month
        '2000-01-32', // invalid day
        '',
        null,
        undefined
      ];

      invalidDates.forEach(date => {
        expect(isValidDate(date as string)).toBe(false);
      });
    });
  });

  describe('isValidZodiacSign', () => {
    test('should validate correct zodiac signs', () => {
      const validSigns = [
        'aries', 'taurus', 'gemini', 'cancer',
        'leo', 'virgo', 'libra', 'scorpio',
        'sagittarius', 'capricorn', 'aquarius', 'pisces'
      ];

      validSigns.forEach(sign => {
        expect(isValidZodiacSign(sign)).toBe(true);
      });
    });

    test('should reject invalid zodiac signs', () => {
      const invalidSigns = [
        'invalid',
        'ARIES', // wrong case
        'ophiuchus',
        '',
        null,
        undefined
      ];

      invalidSigns.forEach(sign => {
        expect(isValidZodiacSign(sign as string)).toBe(false);
      });
    });
  });

  describe('isValidLanguage', () => {
    test('should validate supported languages', () => {
      expect(isValidLanguage('en')).toBe(true);
      expect(isValidLanguage('si')).toBe(true);
    });

    test('should reject unsupported languages', () => {
      const invalidLanguages = ['fr', 'de', 'es', '', null, undefined];
      
      invalidLanguages.forEach(lang => {
        expect(isValidLanguage(lang as string)).toBe(false);
      });
    });
  });

  describe('isValidHoroscopeType', () => {
    test('should validate supported horoscope types', () => {
      expect(isValidHoroscopeType('daily')).toBe(true);
      expect(isValidHoroscopeType('weekly')).toBe(true);
      expect(isValidHoroscopeType('monthly')).toBe(true);
    });

    test('should reject unsupported horoscope types', () => {
      const invalidTypes = ['yearly', 'hourly', '', null, undefined];
      
      invalidTypes.forEach(type => {
        expect(isValidHoroscopeType(type as string)).toBe(false);
      });
    });
  });

  describe('isValidContent', () => {
    test('should validate safe content', () => {
      const validContent = [
        'This is a valid horoscope content.',
        'Today brings new opportunities for growth and adventure.',
        'A longer piece of content that contains multiple sentences and provides detailed guidance for the user.'
      ];

      validContent.forEach(content => {
        expect(isValidContent(content)).toBe(true);
      });
    });

    test('should reject unsafe or invalid content', () => {
      const invalidContent = [
        '', // empty
        'Short', // too short
        'A'.repeat(5001), // too long
        '<script>alert("xss")</script>', // script tag
        'javascript:alert("xss")', // javascript protocol
        '<iframe src="evil.com"></iframe>', // iframe
        'onclick="alert()"', // event handler
        null,
        undefined
      ];

      invalidContent.forEach(content => {
        expect(isValidContent(content as string)).toBe(false);
      });
    });
  });

  describe('isValidUUID', () => {
    test('should validate correct UUID formats', () => {
      const validUUIDs = [
        '550e8400-e29b-41d4-a716-************',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '6ba7b810-9dad-11d1-80b4-00c04fd430c8'
      ];

      validUUIDs.forEach(uuid => {
        expect(isValidUUID(uuid)).toBe(true);
      });
    });

    test('should reject invalid UUID formats', () => {
      const invalidUUIDs = [
        'not-a-uuid',
        '550e8400-e29b-41d4-a716', // too short
        '550e8400-e29b-41d4-a716-************-extra', // too long
        '',
        null,
        undefined
      ];

      invalidUUIDs.forEach(uuid => {
        expect(isValidUUID(uuid as string)).toBe(false);
      });
    });
  });

  describe('sanitizeString', () => {
    test('should sanitize dangerous input', () => {
      expect(sanitizeString('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script');
      expect(sanitizeString('javascript:alert("test")')).toBe('alert("test")');
      expect(sanitizeString('onclick="alert()"')).toBe('"alert()"');
      expect(sanitizeString('  test  ')).toBe('test');
    });

    test('should handle edge cases', () => {
      expect(sanitizeString('')).toBe('');
      expect(sanitizeString(null as any)).toBe('');
      expect(sanitizeString(undefined as any)).toBe('');
    });

    test('should limit string length', () => {
      const longString = 'A'.repeat(2000);
      const result = sanitizeString(longString);
      expect(result.length).toBe(1000);
    });
  });

  describe('validatePagination', () => {
    test('should return valid pagination with defaults', () => {
      expect(validatePagination()).toEqual({ page: 1, limit: 10 });
      expect(validatePagination(undefined, undefined)).toEqual({ page: 1, limit: 10 });
    });

    test('should parse valid pagination parameters', () => {
      expect(validatePagination('5', '20')).toEqual({ page: 5, limit: 20 });
      expect(validatePagination('1', '50')).toEqual({ page: 1, limit: 50 });
    });

    test('should enforce limits', () => {
      expect(validatePagination('0', '0')).toEqual({ page: 1, limit: 1 });
      expect(validatePagination('2000', '200')).toEqual({ page: 1000, limit: 100 });
      expect(validatePagination('-1', '-5')).toEqual({ page: 1, limit: 1 });
    });

    test('should handle invalid input', () => {
      expect(validatePagination('invalid', 'also-invalid')).toEqual({ page: 1, limit: 10 });
      expect(validatePagination('', '')).toEqual({ page: 1, limit: 10 });
    });
  });

  describe('isValidSearchQuery', () => {
    test('should validate safe search queries', () => {
      const validQueries = [
        'john',
        '<EMAIL>',
        'search term',
        'test123'
      ];

      validQueries.forEach(query => {
        expect(isValidSearchQuery(query)).toBe(true);
      });
    });

    test('should reject dangerous search queries', () => {
      const dangerousQueries = [
        'UNION SELECT * FROM users',
        'DROP TABLE users',
        'DELETE FROM users',
        'INSERT INTO users',
        'UPDATE users SET',
        'SELECT * FROM users --',
        'test /* comment */',
        '',
        'A'.repeat(101), // too long
        null,
        undefined
      ];

      dangerousQueries.forEach(query => {
        expect(isValidSearchQuery(query as string)).toBe(false);
      });
    });
  });

  describe('validateRequestBody', () => {
    test('should validate request body with all required fields', () => {
      const body = { name: 'John', email: '<EMAIL>', age: 30 };
      const requiredFields = ['name', 'email'];
      
      const result = validateRequestBody(body, requiredFields);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect missing required fields', () => {
      const body = { name: 'John' };
      const requiredFields = ['name', 'email', 'age'];
      
      const result = validateRequestBody(body, requiredFields);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing required field: email');
      expect(result.errors).toContain('Missing required field: age');
    });

    test('should handle invalid request body', () => {
      const result = validateRequestBody(null, ['name']);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid request body');
    });

    test('should handle null and undefined values', () => {
      const body = { name: 'John', email: null, age: undefined };
      const requiredFields = ['name', 'email', 'age'];
      
      const result = validateRequestBody(body, requiredFields);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing required field: email');
      expect(result.errors).toContain('Missing required field: age');
    });
  });
});
