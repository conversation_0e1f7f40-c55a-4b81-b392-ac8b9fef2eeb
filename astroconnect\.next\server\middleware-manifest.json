{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b618H/y1SLZywAkLdpd8YhNKp6xbjpdDK+HBLp+oGsE=", "__NEXT_PREVIEW_MODE_ID": "e2c1119eb0db0b60b3d06a17fe539dc4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0fe8e4910f5d23e769e717de06f44430fcabf7bb8037e708a80b506ccb545d7e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f512e656f710067af7c5412aa7373b3c3dbd659d33acc1b2d384b3765e3b3a9f"}}}, "sortedMiddleware": ["/"], "functions": {}}