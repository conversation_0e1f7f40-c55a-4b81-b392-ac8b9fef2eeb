{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b618H/y1SLZywAkLdpd8YhNKp6xbjpdDK+HBLp+oGsE=", "__NEXT_PREVIEW_MODE_ID": "d479371eb7445ecf01857d3586447fd8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d8010a13a2ea1bc9b035afb82039afb985231acf86a69bf2f8aff286804e1cbd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "027091f85f519ed104be2571d4493bcdcc5d75adb859a6edb123cfb540e02840"}}}, "sortedMiddleware": ["/"], "functions": {}}