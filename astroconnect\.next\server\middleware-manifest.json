{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "639rRtKH+NUZDwXg7HNSOFhkQsSObLciaV8aTeAVK14=", "__NEXT_PREVIEW_MODE_ID": "12a9f55fd78a74d4765613f2a1fdf149", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "98243f792eb55fa99b8aee02427f79e89982f0b4bb021bd232435724f3e2912a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f45ea94422ea37350072d0191346c691c162537951fc1c104ef9fd449f24cd72"}}}, "sortedMiddleware": ["/"], "functions": {}}