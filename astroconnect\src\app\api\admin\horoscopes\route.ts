import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin, TABLES } from '@/lib/supabase';
import { ApiResponse, Horoscope } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { zodiacSign, type, content, date, language = 'en' } = await request.json();

    if (!zodiacSign || !type || !content || !date) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Missing required fields: zodiacSign, type, content, date'
      }, { status: 400 });
    }

    const { data: horoscope, error } = await supabaseAdmin
      .from(TABLES.HOROSCOPES)
      .insert({
        zodiac_sign: zodiacSign,
        type,
        content,
        date,
        language
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating horoscope:', error);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Failed to create horoscope'
      }, { status: 500 });
    }

    return NextResponse.json<ApiResponse<Horoscope>>({
      success: true,
      data: horoscope as Horoscope,
      message: 'Horoscope created successfully'
    });

  } catch (error) {
    console.error('Horoscope creation error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const zodiacSign = searchParams.get('zodiacSign');
  const type = searchParams.get('type');
  const date = searchParams.get('date');
  const language = searchParams.get('language') || 'en';
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '20');
  const offset = (page - 1) * limit;

  try {
    let query = supabaseAdmin
      .from(TABLES.HOROSCOPES)
      .select('*')
      .eq('language', language)
      .order('date', { ascending: false })
      .order('zodiac_sign')
      .range(offset, offset + limit - 1);

    if (zodiacSign) {
      query = query.eq('zodiac_sign', zodiacSign);
    }

    if (type) {
      query = query.eq('type', type);
    }

    if (date) {
      query = query.eq('date', date);
    }

    const { data: horoscopes, error } = await query;

    if (error) {
      console.error('Error fetching horoscopes:', error);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Failed to fetch horoscopes'
      }, { status: 500 });
    }

    return NextResponse.json<ApiResponse<Horoscope[]>>({
      success: true,
      data: horoscopes as Horoscope[],
      message: 'Horoscopes retrieved successfully'
    });

  } catch (error) {
    console.error('Horoscopes fetch error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, zodiacSign, type, content, date, language } = await request.json();

    if (!id) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Horoscope ID is required'
      }, { status: 400 });
    }

    const updateData: any = {};
    if (zodiacSign) updateData.zodiac_sign = zodiacSign;
    if (type) updateData.type = type;
    if (content) updateData.content = content;
    if (date) updateData.date = date;
    if (language) updateData.language = language;

    const { data: horoscope, error } = await supabaseAdmin
      .from(TABLES.HOROSCOPES)
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating horoscope:', error);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Failed to update horoscope'
      }, { status: 500 });
    }

    return NextResponse.json<ApiResponse<Horoscope>>({
      success: true,
      data: horoscope as Horoscope,
      message: 'Horoscope updated successfully'
    });

  } catch (error) {
    console.error('Horoscope update error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Horoscope ID is required'
    }, { status: 400 });
  }

  try {
    const { error } = await supabaseAdmin
      .from(TABLES.HOROSCOPES)
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting horoscope:', error);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Failed to delete horoscope'
      }, { status: 500 });
    }

    return NextResponse.json<ApiResponse<{ deleted: boolean }>>({
      success: true,
      data: { deleted: true },
      message: 'Horoscope deleted successfully'
    });

  } catch (error) {
    console.error('Horoscope deletion error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
