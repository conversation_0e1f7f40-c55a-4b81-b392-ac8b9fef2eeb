import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ApiResponse, Horoscope, ZodiacSign, LanguageCode, HoroscopeType } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { zodiacSign, type, content, date, language = 'en' } = await request.json();

    if (!zodiacSign || !type || !content || !date) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Missing required fields: zodiacSign, type, content, date'
      }, { status: 400 });
    }

    const horoscope = await prisma.horoscope.create({
      data: {
        zodiacSign: zodiacSign as ZodiacSign,
        type: type as HoroscopeType,
        content,
        date: new Date(date),
        language: language as LanguageCode
      }
    });

    return NextResponse.json<ApiResponse<Horoscope>>({
      success: true,
      data: horoscope as Horoscope,
      message: 'Horoscope created successfully'
    });

  } catch (error) {
    console.error('Horoscope creation error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const zodiacSign = searchParams.get('zodiacSign');
  const type = searchParams.get('type');
  const date = searchParams.get('date');
  const language = searchParams.get('language') || 'en';
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '20');
  const offset = (page - 1) * limit;

  try {
    // Build where clause
    const where: any = {
      language: language as LanguageCode
    };

    if (zodiacSign) {
      where.zodiacSign = zodiacSign as ZodiacSign;
    }

    if (type) {
      where.type = type as HoroscopeType;
    }

    if (date) {
      where.date = new Date(date);
    }

    const horoscopes = await prisma.horoscope.findMany({
      where,
      orderBy: [
        { date: 'desc' },
        { zodiacSign: 'asc' }
      ],
      skip: offset,
      take: limit
    });

    return NextResponse.json<ApiResponse<Horoscope[]>>({
      success: true,
      data: horoscopes,
      message: 'Horoscopes retrieved successfully'
    });

  } catch (error) {
    console.error('Horoscopes fetch error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, zodiacSign, type, content, date, language } = await request.json();

    if (!id) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Horoscope ID is required'
      }, { status: 400 });
    }

    const updateData: any = {};
    if (zodiacSign) updateData.zodiacSign = zodiacSign as ZodiacSign;
    if (type) updateData.type = type as HoroscopeType;
    if (content) updateData.content = content;
    if (date) updateData.date = new Date(date);
    if (language) updateData.language = language as LanguageCode;

    const horoscope = await prisma.horoscope.update({
      where: { id },
      data: updateData
    });

    return NextResponse.json<ApiResponse<Horoscope>>({
      success: true,
      data: horoscope,
      message: 'Horoscope updated successfully'
    });

  } catch (error) {
    console.error('Horoscope update error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Horoscope ID is required'
    }, { status: 400 });
  }

  try {
    await prisma.horoscope.delete({
      where: { id }
    });

    return NextResponse.json<ApiResponse<{ deleted: boolean }>>({
      success: true,
      data: { deleted: true },
      message: 'Horoscope deleted successfully'
    });

  } catch (error) {
    console.error('Horoscope deletion error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
