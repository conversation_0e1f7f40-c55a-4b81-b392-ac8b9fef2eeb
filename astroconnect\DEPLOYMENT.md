# AstroConnect Deployment Guide

This guide covers the deployment of the AstroConnect horoscope QR webapp to production environments.

## Prerequisites

### System Requirements
- **Node.js**: 18.x or higher
- **Docker**: 20.x or higher
- **Docker Compose**: 2.x or higher
- **SSL Certificate**: For HTTPS (Let's Encrypt recommended)
- **Domain**: Configured and pointing to your server

### Required Services
- **Supabase**: PostgreSQL database and authentication
- **Gemini API**: For translation services
- **Server**: VPS or cloud instance (minimum 2GB RAM, 2 CPU cores)

## Environment Configuration

### 1. Environment Variables

Create a `.env.local` file with the following variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NODE_ENV=production
```

### 2. Database Setup

1. Create a new Supabase project
2. Run the SQL schema from `database/schema.sql`
3. Optionally load sample data from `database/sample_data.sql`
4. Configure Row Level Security policies
5. Update environment variables with your Supabase credentials

## Deployment Options

### Option 1: Docker Deployment (Recommended)

#### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd astroconnect

# Configure environment variables
cp .env.example .env.local
# Edit .env.local with your actual values

# Deploy with Docker Compose
docker-compose up -d

# Check health
curl http://localhost:3000/api/health
```

#### Production Deployment with SSL
```bash
# Build and deploy with Nginx proxy
docker-compose --profile production up -d

# Check logs
docker-compose logs -f
```

### Option 2: Manual Deployment

#### Build and Start
```bash
# Install dependencies
npm install

# Build the application
npm run build

# Start in production mode
npm start
```

#### Using PM2 (Process Manager)
```bash
# Install PM2 globally
npm install -g pm2

# Start with PM2
pm2 start npm --name "astroconnect" -- start

# Save PM2 configuration
pm2 save
pm2 startup
```

## SSL Certificate Setup

### Using Let's Encrypt with Certbot

```bash
# Install Certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal (add to crontab)
0 12 * * * /usr/bin/certbot renew --quiet
```

### Manual Certificate Setup

1. Place your SSL certificate files in the `ssl/` directory:
   - `cert.pem` - SSL certificate
   - `key.pem` - Private key

2. Update the Nginx configuration if needed

## Monitoring and Maintenance

### Health Checks

The application provides a health check endpoint at `/api/health`:

```bash
# Check application health
curl https://yourdomain.com/api/health

# Expected response
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "production",
  "checks": {
    "database": "ok",
    "api": "ok",
    "environment": "ok"
  },
  "uptime": 3600
}
```

### Logging

#### Docker Logs
```bash
# View application logs
docker-compose logs -f astroconnect

# View Nginx logs
docker-compose logs -f nginx
```

#### Application Logs
Logs are written to:
- Application: `stdout/stderr` (captured by Docker)
- Nginx: `/var/log/nginx/` (inside container)
- Deployment: `./deploy.log`

### Backup Strategy

#### Database Backup
```bash
# Backup Supabase database
# Use Supabase dashboard or CLI tools
supabase db dump --db-url "your-database-url" > backup.sql
```

#### Application Backup
```bash
# Create application backup
./scripts/deploy.sh backup

# Backups are stored in ./backups/ directory
```

## Performance Optimization

### Nginx Configuration
- Gzip compression enabled
- Static file caching (1 year)
- Rate limiting configured
- Security headers applied

### Application Optimization
- Next.js static optimization
- Image optimization
- Code splitting
- Service Worker for offline functionality

## Security Considerations

### Network Security
- HTTPS enforced
- HSTS headers
- Rate limiting on API endpoints
- Input validation and sanitization

### Application Security
- Environment variables secured
- CORS properly configured
- XSS protection enabled
- SQL injection prevention

### Monitoring
- Health check endpoint
- Error logging
- Performance monitoring
- Security audit logs

## Troubleshooting

### Common Issues

#### Application Won't Start
1. Check environment variables
2. Verify database connectivity
3. Check Docker logs
4. Ensure ports are available

#### Database Connection Issues
1. Verify Supabase credentials
2. Check network connectivity
3. Validate database schema
4. Review RLS policies

#### SSL Certificate Issues
1. Verify certificate files
2. Check domain configuration
3. Validate certificate expiry
4. Review Nginx configuration

### Debug Commands

```bash
# Check container status
docker ps

# View detailed logs
docker-compose logs --tail=100 astroconnect

# Test database connection
docker exec -it astroconnect-app node -e "
const { supabase } = require('./src/lib/supabase');
supabase.from('users').select('count').then(console.log);
"

# Test API endpoints
curl -v https://yourdomain.com/api/health
```

## Scaling Considerations

### Horizontal Scaling
- Load balancer configuration
- Session management
- Database connection pooling
- CDN for static assets

### Vertical Scaling
- Increase server resources
- Optimize database queries
- Implement caching strategies
- Monitor resource usage

## Maintenance Schedule

### Daily
- Monitor health checks
- Review error logs
- Check system resources

### Weekly
- Update dependencies
- Review security logs
- Performance analysis
- Backup verification

### Monthly
- Security updates
- SSL certificate renewal check
- Database optimization
- Capacity planning review

## Support and Documentation

- **Application Logs**: Check Docker logs for application issues
- **Database Issues**: Refer to Supabase documentation
- **SSL Problems**: Check Let's Encrypt documentation
- **Performance**: Use built-in monitoring tools

For additional support, refer to the project documentation or contact the development team.
