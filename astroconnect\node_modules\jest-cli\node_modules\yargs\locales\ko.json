{"Commands:": "명령:", "Options:": "옵션:", "Examples:": "예시:", "boolean": "불리언", "count": "개수", "string": "문자열", "number": "숫자", "array": "배열", "required": "필수", "default": "기본값", "default:": "기본값:", "choices:": "선택지:", "aliases:": "별칭:", "generated-value": "생성된 값", "Not enough non-option arguments: got %s, need at least %s": {"one": "옵션이 아닌 인수가 충분하지 않습니다: %s개 입력받음, 최소 %s개 입력 필요", "other": "옵션이 아닌 인수가 충분하지 않습니다: %s개 입력받음, 최소 %s개 입력 필요"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "옵션이 아닌 인수가 너무 많습니다: %s개 입력받음, 최대 %s개 입력 가능", "other": "옵션이 아닌 인수가 너무 많습니다: %s개 입력받음, 최대 %s개 입력 가능"}, "Missing argument value: %s": {"one": "인수가 주어지지 않았습니다: %s", "other": "인수가 주어지지 않았습니다: %s"}, "Missing required argument: %s": {"one": "필수 인수가 주어지지 않았습니다: %s", "other": "필수 인수가 주어지지 않았습니다: %s"}, "Unknown argument: %s": {"one": "알 수 없는 인수입니다: %s", "other": "알 수 없는 인수입니다: %s"}, "Invalid values:": "유효하지 않은 값:", "Argument: %s, Given: %s, Choices: %s": "인수: %s, 주어진 값: %s, 선택지: %s", "Argument check failed: %s": "인수 체크에 실패했습니다: %s", "Implications failed:": "주어진 인수에 필요한 추가 인수가 주어지지 않았습니다:", "Not enough arguments following: %s": "다음 인수가 주어지지 않았습니다: %s", "Invalid JSON config file: %s": "유효하지 않은 JSON 설정 파일: %s", "Path to JSON config file": "JSON 설정 파일 경로", "Show help": "도움말 표시", "Show version number": "버전 표시", "Did you mean %s?": "%s을(를) 찾으시나요?", "Arguments %s and %s are mutually exclusive": "인수 %s과(와) %s은(는) 동시에 지정할 수 없습니다", "Positionals:": "위치:", "command": "명령"}