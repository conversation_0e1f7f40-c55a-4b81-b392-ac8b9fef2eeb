import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateQRCodeImage, generateQRToken } from '@/utils/qr';
import { getZodiacFromDate } from '@/utils/zodiac';
import { ApiResponse, User, ZodiacSign, LanguageCode } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { name, email, birthDate, languagePreference = 'en' } = await request.json();

    if (!name || !birthDate) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Name and birth date are required'
      }, { status: 400 });
    }

    // Calculate zodiac sign from birth date
    const zodiacSign = getZodiacFromDate(birthDate) as ZodiacSign;
    const qrToken = generateQRToken();

    // Create user in database
    const user = await prisma.user.create({
      data: {
        name,
        email,
        birthDate: new Date(birthDate),
        zodiacSign,
        languagePreference: languagePreference as LanguageCode,
        qrToken
      }
    });

    // Generate QR code image
    const qrCodeImage = await generateQRCodeImage(user.qr_token);

    return NextResponse.json<ApiResponse<{ user: User; qrCodeImage: string }>>({
      success: true,
      data: {
        user: user as User,
        qrCodeImage
      },
      message: 'User created successfully with QR code'
    });

  } catch (error) {
    console.error('User creation error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const offset = (page - 1) * limit;

  try {
    // Get users with pagination
    const users = await prisma.user.findMany({
      include: {
        qrCodeMappings: {
          select: {
            scanCount: true,
            lastScanned: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limit
    });

    // Get total count
    const total = await prisma.user.count();

    return NextResponse.json<ApiResponse<{ users: any[]; total: number; page: number; limit: number }>>({
      success: true,
      data: {
        users,
        total,
        page,
        limit
      },
      message: 'Users retrieved successfully'
    });

  } catch (error) {
    console.error('Users fetch error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');

  if (!userId) {
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'User ID is required'
    }, { status: 400 });
  }

  try {
    await prisma.user.delete({
      where: { id: userId }
    });

    return NextResponse.json<ApiResponse<{ deleted: boolean }>>({
      success: true,
      data: { deleted: true },
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('User deletion error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
