import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin, TABLES } from '@/lib/supabase';
import { generateQRCodeImage } from '@/utils/qr';
import { getZodiacFromDate } from '@/utils/zodiac';
import { ApiResponse, User } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { name, email, birthDate, languagePreference = 'en' } = await request.json();

    if (!name || !birthDate) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Name and birth date are required'
      }, { status: 400 });
    }

    // Calculate zodiac sign from birth date
    const zodiacSign = getZodiacFromDate(birthDate);

    // Create user in database (QR token will be auto-generated)
    const { data: user, error: userError } = await supabaseAdmin
      .from(TABLES.USERS)
      .insert({
        name,
        email,
        birth_date: birthDate,
        zodiac_sign: zodiacSign,
        language_preference: languagePreference
      })
      .select()
      .single();

    if (userError) {
      console.error('Error creating user:', userError);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Failed to create user'
      }, { status: 500 });
    }

    // Generate QR code image
    const qrCodeImage = await generateQRCodeImage(user.qr_token);

    return NextResponse.json<ApiResponse<{ user: User; qrCodeImage: string }>>({
      success: true,
      data: {
        user: user as User,
        qrCodeImage
      },
      message: 'User created successfully with QR code'
    });

  } catch (error) {
    console.error('User creation error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const offset = (page - 1) * limit;

  try {
    // Get users with pagination
    const { data: users, error: usersError } = await supabaseAdmin
      .from(TABLES.USERS)
      .select(`
        *,
        qr_code_mappings (
          scan_count,
          last_scanned
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Failed to fetch users'
      }, { status: 500 });
    }

    // Get total count
    const { count, error: countError } = await supabaseAdmin
      .from(TABLES.USERS)
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error counting users:', countError);
    }

    return NextResponse.json<ApiResponse<{ users: any[]; total: number; page: number; limit: number }>>({
      success: true,
      data: {
        users: users || [],
        total: count || 0,
        page,
        limit
      },
      message: 'Users retrieved successfully'
    });

  } catch (error) {
    console.error('Users fetch error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');

  if (!userId) {
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'User ID is required'
    }, { status: 400 });
  }

  try {
    const { error } = await supabaseAdmin
      .from(TABLES.USERS)
      .delete()
      .eq('id', userId);

    if (error) {
      console.error('Error deleting user:', error);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Failed to delete user'
      }, { status: 500 });
    }

    return NextResponse.json<ApiResponse<{ deleted: boolean }>>({
      success: true,
      data: { deleted: true },
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('User deletion error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
