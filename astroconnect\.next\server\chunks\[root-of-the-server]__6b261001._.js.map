{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/test/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nexport async function GET() {\n  return NextResponse.json({\n    success: true,\n    message: 'AstroConnect API is working!',\n    timestamp: new Date().toISOString(),\n    database: 'Prisma with PostgreSQL (not connected in demo)',\n    features: [\n      'QR Code Authentication',\n      'Multi-language Support (English/Sinhala)',\n      'Daily Horoscopes',\n      'Lucky Guidance',\n      'Admin Panel',\n      'Progressive Web App'\n    ]\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;QACjC,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}]}