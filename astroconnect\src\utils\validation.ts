import { ZodiacSign, LanguageCode, HoroscopeType } from '@prisma/client';
import { ZODIAC_SIGNS } from './zodiac';

// Email validation
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

// Name validation
export function isValidName(name: string): boolean {
  if (!name || typeof name !== 'string') return false;
  
  // Remove extra whitespace and check length
  const trimmedName = name.trim();
  if (trimmedName.length < 2 || trimmedName.length > 100) return false;
  
  // Check for valid characters (letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-Z\s\-']+$/;
  return nameRegex.test(trimmedName);
}

// Date validation
export function isValidDate(dateString: string): boolean {
  if (!dateString || typeof dateString !== 'string') return false;
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return false;
  
  // Check if date is reasonable (not in future, not too far in past)
  const now = new Date();
  const minDate = new Date('1900-01-01');
  
  return date <= now && date >= minDate;
}

// Zodiac sign validation
export function isValidZodiacSign(sign: string): sign is ZodiacSign {
  return ZODIAC_SIGNS.includes(sign as ZodiacSign);
}

// Language validation
export function isValidLanguage(lang: string): lang is LanguageCode {
  return lang === 'en' || lang === 'si';
}

// Horoscope type validation
export function isValidHoroscopeType(type: string): type is HoroscopeType {
  return ['daily', 'weekly', 'monthly'].includes(type);
}

// Content validation (for horoscopes, advice, etc.)
export function isValidContent(content: string): boolean {
  if (!content || typeof content !== 'string') return false;
  
  const trimmedContent = content.trim();
  if (trimmedContent.length < 10 || trimmedContent.length > 5000) return false;
  
  // Check for potentially malicious content
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i,
    /<object/i,
    /<embed/i
  ];
  
  return !suspiciousPatterns.some(pattern => pattern.test(content));
}

// UUID validation
export function isValidUUID(uuid: string): boolean {
  if (!uuid || typeof uuid !== 'string') return false;
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// Sanitize string input
export function sanitizeString(input: string): string {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
}

// Sanitize HTML content (basic)
export function sanitizeHTML(html: string): string {
  if (!html || typeof html !== 'string') return '';
  
  // Allow only basic formatting tags
  const allowedTags = ['p', 'br', 'strong', 'em', 'u'];
  const tagRegex = /<\/?(\w+)[^>]*>/g;
  
  return html.replace(tagRegex, (match, tagName) => {
    if (allowedTags.includes(tagName.toLowerCase())) {
      return match;
    }
    return '';
  });
}

// Validate pagination parameters
export function validatePagination(page?: string, limit?: string): { page: number; limit: number } {
  const parsedPage = parseInt(page || '1', 10);
  const parsedLimit = parseInt(limit || '10', 10);
  
  return {
    page: Math.max(1, Math.min(parsedPage, 1000)), // Max 1000 pages
    limit: Math.max(1, Math.min(parsedLimit, 100)) // Max 100 items per page
  };
}

// Validate search query
export function isValidSearchQuery(query: string): boolean {
  if (!query || typeof query !== 'string') return false;
  
  const trimmedQuery = query.trim();
  if (trimmedQuery.length < 1 || trimmedQuery.length > 100) return false;
  
  // Check for SQL injection patterns
  const sqlPatterns = [
    /union\s+select/i,
    /drop\s+table/i,
    /delete\s+from/i,
    /insert\s+into/i,
    /update\s+set/i,
    /--/,
    /\/\*/,
    /\*\//
  ];
  
  return !sqlPatterns.some(pattern => pattern.test(query));
}

// Rate limiting key generation
export function generateRateLimitKey(ip: string, endpoint: string, userId?: string): string {
  const baseKey = `${ip}:${endpoint}`;
  return userId ? `${baseKey}:${userId}` : baseKey;
}

// Password strength validation (if implementing password auth)
export function isStrongPassword(password: string): boolean {
  if (!password || typeof password !== 'string') return false;
  
  // At least 8 characters, contains uppercase, lowercase, number, and special character
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return strongPasswordRegex.test(password) && password.length <= 128;
}

// Validate API request body
export function validateRequestBody(body: any, requiredFields: string[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!body || typeof body !== 'object') {
    return { isValid: false, errors: ['Invalid request body'] };
  }
  
  // Check required fields
  for (const field of requiredFields) {
    if (!(field in body) || body[field] === null || body[field] === undefined) {
      errors.push(`Missing required field: ${field}`);
    }
  }
  
  return { isValid: errors.length === 0, errors };
}

// Escape special characters for database queries
export function escapeSpecialChars(input: string): string {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .replace(/'/g, "''") // Escape single quotes
    .replace(/\\/g, '\\\\') // Escape backslashes
    .replace(/\0/g, '\\0') // Escape null bytes
    .replace(/\n/g, '\\n') // Escape newlines
    .replace(/\r/g, '\\r') // Escape carriage returns
    .replace(/\x1a/g, '\\Z'); // Escape ctrl+Z
}
