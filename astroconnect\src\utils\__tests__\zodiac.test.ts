import { getZodiacFromDate, getZodiacColors, ZODIAC_INFO, ZODIAC_SIGNS } from '../zodiac';

describe('Zodiac Utilities', () => {
  describe('getZodiacFromDate', () => {
    test('should return correct zodiac sign for <PERSON><PERSON>', () => {
      expect(getZodiacFromDate('1990-04-15')).toBe('aries');
      expect(getZodiacFromDate('1990-03-21')).toBe('aries');
      expect(getZodiacFromDate('1990-04-19')).toBe('aries');
    });

    test('should return correct zodiac sign for <PERSON>', () => {
      expect(getZodiacFromDate('1985-08-10')).toBe('leo');
      expect(getZodiacFromDate('1985-07-23')).toBe('leo');
      expect(getZodiacFromDate('1985-08-22')).toBe('leo');
    });

    test('should return correct zodiac sign for <PERSON><PERSON><PERSON>', () => {
      expect(getZodiacFromDate('1992-11-05')).toBe('scorpio');
      expect(getZodiacFromDate('1992-10-23')).toBe('scorpio');
      expect(getZodiacFromDate('1992-11-21')).toBe('scorpio');
    });

    test('should return correct zodiac sign for Pisces', () => {
      expect(getZodiacFromDate('1988-03-12')).toBe('pisces');
      expect(getZodiacFromDate('1988-02-19')).toBe('pisces');
      expect(getZodiacFromDate('1988-03-20')).toBe('pisces');
    });

    test('should handle edge cases correctly', () => {
      expect(getZodiacFromDate('2000-01-01')).toBe('capricorn');
      expect(getZodiacFromDate('2000-12-31')).toBe('capricorn');
      expect(getZodiacFromDate('2000-06-21')).toBe('cancer');
      expect(getZodiacFromDate('2000-12-21')).toBe('sagittarius');
    });
  });

  describe('getZodiacColors', () => {
    test('should return array of colors for each zodiac sign', () => {
      ZODIAC_SIGNS.forEach(sign => {
        const colors = getZodiacColors(sign);
        expect(Array.isArray(colors)).toBe(true);
        expect(colors).toHaveLength(2);
        expect(typeof colors[0]).toBe('string');
        expect(typeof colors[1]).toBe('string');
        expect(colors[0]).toMatch(/^#[0-9A-F]{6}$/i);
        expect(colors[1]).toMatch(/^#[0-9A-F]{6}$/i);
      });
    });

    test('should return specific colors for known signs', () => {
      const ariesColors = getZodiacColors('aries');
      expect(ariesColors).toEqual(['#FF6B6B', '#FF4757']);

      const leoColors = getZodiacColors('leo');
      expect(leoColors).toEqual(['#E74C3C', '#C0392B']);
    });
  });

  describe('ZODIAC_INFO', () => {
    test('should contain information for all zodiac signs', () => {
      ZODIAC_SIGNS.forEach(sign => {
        expect(ZODIAC_INFO[sign]).toBeDefined();
        expect(ZODIAC_INFO[sign].name).toBeDefined();
        expect(ZODIAC_INFO[sign].symbol).toBeDefined();
        expect(ZODIAC_INFO[sign].dates).toBeDefined();
        expect(ZODIAC_INFO[sign].element).toBeDefined();
      });
    });

    test('should have correct elements for signs', () => {
      expect(ZODIAC_INFO.aries.element).toBe('Fire');
      expect(ZODIAC_INFO.taurus.element).toBe('Earth');
      expect(ZODIAC_INFO.gemini.element).toBe('Air');
      expect(ZODIAC_INFO.cancer.element).toBe('Water');
    });

    test('should have valid Unicode symbols', () => {
      ZODIAC_SIGNS.forEach(sign => {
        const symbol = ZODIAC_INFO[sign].symbol;
        expect(symbol).toMatch(/^[\u2600-\u26FF]$/); // Unicode symbols range
      });
    });
  });

  describe('ZODIAC_SIGNS', () => {
    test('should contain exactly 12 zodiac signs', () => {
      expect(ZODIAC_SIGNS).toHaveLength(12);
    });

    test('should contain all expected zodiac signs', () => {
      const expectedSigns = [
        'aries', 'taurus', 'gemini', 'cancer',
        'leo', 'virgo', 'libra', 'scorpio',
        'sagittarius', 'capricorn', 'aquarius', 'pisces'
      ];
      
      expectedSigns.forEach(sign => {
        expect(ZODIAC_SIGNS).toContain(sign);
      });
    });
  });
});
