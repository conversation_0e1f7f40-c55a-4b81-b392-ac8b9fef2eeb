**Project Name:** AstroConnect – Personalized Horoscope & Daily Guide Web App

---

### **1. Overview**

AstroConnect is a fully mobile-optimized astrology web app designed to provide users with personalized horoscopes and daily guidance. The app leverages static, unique QR codes printed on personalized cards to deliver a seamless login experience. Upon scanning the QR code, users are redirected to their unique dashboard. The app supports English and Sinhala languages, with real-time language translation powered by the Gemini API.

---

### **2. Key Features**

#### **2.1. QR-Based Auto Login**

- Each printed card has a static, unique QR code.
- When scanned, the QR code redirects the user to the web app and logs them in automatically.
- QR codes are permanently mapped to user profiles in the backend securely.

#### **2.2. Personalized Dashboard**

Upon login via QR or regular login:

- **Horoscope Page:**
  - Daily, weekly, and monthly horoscopes
  - Zodiac sign-based predictions
- **Daily Guide Page:**
  - Personalized daily suggestions
  - Lucky number, color, time, etc.

#### **2.3. Landing Page**

For users visiting the site directly:

- App introduction and benefits
- Option to scan QR code via camera or upload image
- Support for login via email (optional fallback)

#### **2.4. Language Translation**

- Default language: English
- Toggle to Sinhala via Gemini API
- All content dynamically translated while preserving meaning/context

#### **2.5. Mobile Optimization**

- Responsive layout for all screen sizes
- Touch-friendly UI components
- Fast loading with progressive enhancements (PWA-ready in future updates)

#### **2.6. Astrology-Themed UI**

- Celestial background, star animations
- Zodiac iconography
- Dark-mode preferred theme
- Elegant, spiritual, and modern design

---

### **3. Technology Stack**

#### **Frontend**

- HTML5, TailwindCSS (for fast and responsive UI)
- JavaScript (with optional framework like Vue.js or Alpine.js)
- QR Code Scanner (WebRTC or `html5-qrcode` library)

#### **Backend**

- Node.js with Express.js or PHP (depending on preference)
- Gemini API for translation integration
- REST API for dashboard and user data access

#### **Database**

- PostgreSQL
  - Tables: Users, Horoscope Data, Daily Guides, QR Code Mappings (static), Translations Cache

---

### **4. User Flow**

#### **Via QR Code**

1. User scans QR → webapp URL with static unique token → auto-login
2. Backend validates token → maps to user → renders dashboard

#### **Via Direct Access**

1. User visits webapp → sees landing page
2. User can scan QR using phone camera
3. If QR found, auto-login occurs
4. If not, option to login manually or request support

---

### **5. Security Considerations**

- Static QR tokens tied permanently to user profiles
- Tokens are non-expiring but cryptographically secure
- All API traffic secured over HTTPS
- Backend input validation and rate limiting

---

### **6. Admin Panel**

- Add/manage users
- Upload horoscope and guide content
- Track scan analytics
- Multi-language content management

---

### **7. Milestones**

- UI/UX Design Mockups
- QR Code System Integration (Static Mapping)
- Dashboard Development
- Language Translation Integration
- Testing & Launch

---

