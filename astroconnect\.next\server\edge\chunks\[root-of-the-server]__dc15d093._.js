(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__dc15d093._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();
// Rate limiting configuration
const RATE_LIMITS = {
    '/api/auth/qr': {
        requests: 10,
        windowMs: 60000
    },
    '/api/translate': {
        requests: 50,
        windowMs: 60000
    },
    '/api/dashboard': {
        requests: 100,
        windowMs: 60000
    },
    '/api/admin': {
        requests: 20,
        windowMs: 60000
    }
};
function getClientIP(request) {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    if (realIP) {
        return realIP;
    }
    return 'unknown';
}
function isRateLimited(ip, endpoint) {
    const config = RATE_LIMITS[endpoint];
    if (!config) return false;
    const key = `${ip}:${endpoint}`;
    const now = Date.now();
    const record = rateLimitStore.get(key);
    if (!record || now > record.resetTime) {
        // Reset or create new record
        rateLimitStore.set(key, {
            count: 1,
            resetTime: now + config.windowMs
        });
        return false;
    }
    if (record.count >= config.requests) {
        return true;
    }
    record.count++;
    return false;
}
function validateInput(request) {
    const contentType = request.headers.get('content-type');
    const method = request.method;
    // Only validate content-type for POST/PUT/PATCH requests with body
    if (method !== 'GET' && method !== 'HEAD' && contentType) {
        const allowedTypes = [
            'application/json',
            'multipart/form-data',
            'application/x-www-form-urlencoded',
            'text/plain'
        ];
        if (!allowedTypes.some((type)=>contentType.includes(type))) {
            return false;
        }
    }
    // Check for suspicious headers
    const suspiciousHeaders = [
        'x-forwarded-host',
        'x-original-url',
        'x-rewrite-url'
    ];
    for (const header of suspiciousHeaders){
        if (request.headers.get(header)) {
            return false;
        }
    }
    return true;
}
function addSecurityHeaders(response) {
    // Security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    // Content Security Policy
    const csp = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self'",
        "connect-src 'self' https://generativelanguage.googleapis.com",
        "media-src 'self'",
        "object-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "frame-ancestors 'none'",
        "upgrade-insecure-requests"
    ].join('; ');
    response.headers.set('Content-Security-Policy', csp);
    return response;
}
function middleware(request) {
    const { pathname } = request.nextUrl;
    const clientIP = getClientIP(request);
    // Skip middleware for static files and Next.js internals
    if (pathname.startsWith('/_next') || pathname.startsWith('/static') || pathname.includes('.') || pathname === '/favicon.ico') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Temporarily disable input validation for debugging
    // if (!validateInput(request)) {
    //   return new NextResponse('Bad Request', { status: 400 });
    // }
    // Rate limiting for API routes
    if (pathname.startsWith('/api/')) {
        const endpoint = Object.keys(RATE_LIMITS).find((key)=>pathname.startsWith(key));
        if (endpoint && isRateLimited(clientIP, endpoint)) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"]('Too Many Requests', {
                status: 429,
                headers: {
                    'Retry-After': '60'
                }
            });
        }
    }
    // Admin route protection
    if (pathname.startsWith('/admin')) {
        // In production, implement proper admin authentication
        const adminToken = request.headers.get('authorization');
        if (!adminToken && !pathname.includes('login')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/admin/login', request.url));
        }
    }
    // Create response and add security headers
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    return addSecurityHeaders(response);
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */ '/((?!_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__dc15d093._.js.map