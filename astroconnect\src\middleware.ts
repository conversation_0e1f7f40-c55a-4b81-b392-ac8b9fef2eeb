import { NextRequest, NextResponse } from 'next/server';

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting configuration
const RATE_LIMITS = {
  '/api/auth/qr': { requests: 10, windowMs: 60000 }, // 10 requests per minute
  '/api/translate': { requests: 50, windowMs: 60000 }, // 50 requests per minute
  '/api/dashboard': { requests: 100, windowMs: 60000 }, // 100 requests per minute
  '/api/admin': { requests: 20, windowMs: 60000 }, // 20 requests per minute for admin
};

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

function isRateLimited(ip: string, endpoint: string): boolean {
  const config = RATE_LIMITS[endpoint as keyof typeof RATE_LIMITS];
  if (!config) return false;

  const key = `${ip}:${endpoint}`;
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // Reset or create new record
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + config.windowMs
    });
    return false;
  }

  if (record.count >= config.requests) {
    return true;
  }

  record.count++;
  return false;
}

function validateInput(request: NextRequest): boolean {
  const contentType = request.headers.get('content-type');
  const method = request.method;

  // Only validate content-type for POST/PUT/PATCH requests with body
  if (method !== 'GET' && method !== 'HEAD' && contentType) {
    const allowedTypes = [
      'application/json',
      'multipart/form-data',
      'application/x-www-form-urlencoded',
      'text/plain'
    ];

    if (!allowedTypes.some(type => contentType.includes(type))) {
      return false;
    }
  }

  // Check for suspicious headers (only check for really dangerous ones)
  const suspiciousHeaders = ['x-original-url', 'x-rewrite-url'];
  for (const header of suspiciousHeaders) {
    if (request.headers.get(header)) {
      return false;
    }
  }

  return true;
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' https://generativelanguage.googleapis.com",
    "media-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; ');
  
  response.headers.set('Content-Security-Policy', csp);

  return response;
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const clientIP = getClientIP(request);

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Input validation
  if (!validateInput(request)) {
    return new NextResponse('Bad Request', { status: 400 });
  }

  // Rate limiting for API routes
  if (pathname.startsWith('/api/')) {
    const endpoint = Object.keys(RATE_LIMITS).find(key => pathname.startsWith(key));
    
    if (endpoint && isRateLimited(clientIP, endpoint)) {
      return new NextResponse('Too Many Requests', { 
        status: 429,
        headers: {
          'Retry-After': '60'
        }
      });
    }
  }

  // Admin route protection
  if (pathname.startsWith('/admin')) {
    // In production, implement proper admin authentication
    const adminToken = request.headers.get('authorization');
    if (!adminToken && !pathname.includes('login')) {
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
  }

  // Create response and add security headers
  const response = NextResponse.next();
  return addSecurityHeaders(response);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
