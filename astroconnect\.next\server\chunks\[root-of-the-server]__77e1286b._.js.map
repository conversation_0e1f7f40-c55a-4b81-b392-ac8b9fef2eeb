{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/health/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { prisma, checkDatabaseConnection } from '@/lib/prisma';\n\nexport async function GET() {\n  try {\n    // Check database connectivity\n    const isDatabaseConnected = await checkDatabaseConnection();\n\n    if (!isDatabaseConnected) {\n      console.error('Database health check failed');\n      return NextResponse.json(\n        {\n          status: 'unhealthy',\n          timestamp: new Date().toISOString(),\n          checks: {\n            database: 'failed',\n            api: 'ok'\n          },\n          error: 'Database connection failed'\n        },\n        { status: 503 }\n      );\n    }\n\n    // Check environment variables\n    const requiredEnvVars = [\n      'DATABASE_URL'\n    ];\n\n    const missingEnvVars = requiredEnvVars.filter(\n      envVar => !process.env[envVar]\n    );\n\n    if (missingEnvVars.length > 0) {\n      return NextResponse.json(\n        {\n          status: 'unhealthy',\n          timestamp: new Date().toISOString(),\n          checks: {\n            database: 'ok',\n            api: 'ok',\n            environment: 'failed'\n          },\n          error: `Missing environment variables: ${missingEnvVars.join(', ')}`\n        },\n        { status: 503 }\n      );\n    }\n\n    return NextResponse.json({\n      status: 'healthy',\n      timestamp: new Date().toISOString(),\n      version: process.env.npm_package_version || '1.0.0',\n      environment: process.env.NODE_ENV || 'development',\n      checks: {\n        database: 'ok',\n        api: 'ok',\n        environment: 'ok'\n      },\n      uptime: process.uptime()\n    });\n\n  } catch (error) {\n    console.error('Health check error:', error);\n    return NextResponse.json(\n      {\n        status: 'unhealthy',\n        timestamp: new Date().toISOString(),\n        checks: {\n          database: 'unknown',\n          api: 'failed'\n        },\n        error: 'Internal server error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,8BAA8B;QAC9B,MAAM,sBAAsB,MAAM,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD;QAExD,IAAI,CAAC,qBAAqB;YACxB,QAAQ,KAAK,CAAC;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;oBACN,UAAU;oBACV,KAAK;gBACP;gBACA,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,kBAAkB;YACtB;SACD;QAED,MAAM,iBAAiB,gBAAgB,MAAM,CAC3C,CAAA,SAAU,CAAC,QAAQ,GAAG,CAAC,OAAO;QAGhC,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;oBACN,UAAU;oBACV,KAAK;oBACL,aAAa;gBACf;gBACA,OAAO,CAAC,+BAA+B,EAAE,eAAe,IAAI,CAAC,OAAO;YACtE,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YAC5C,aAAa,mDAAwB;YACrC,QAAQ;gBACN,UAAU;gBACV,KAAK;gBACL,aAAa;YACf;YACA,QAAQ,QAAQ,MAAM;QACxB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;gBACN,UAAU;gBACV,KAAK;YACP;YACA,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}